/* Custom styles for Formulation Science AI */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

/* Card Enhancements */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Button Enhancements */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
}

/* Form Enhancements */
.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Table Enhancements */
.table {
    border-radius: 0.375rem;
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid var(--primary-color);
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Prediction Results */
.prediction-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.prediction-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.confidence-interval {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Ingredient Selection */
.ingredient-row {
    background-color: var(--light-color);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
    border: 1px solid #dee2e6;
}

.ingredient-row:hover {
    background-color: #e9ecef;
}

.ingredient-remove {
    color: var(--danger-color);
    cursor: pointer;
    font-size: 1.2rem;
}

.ingredient-remove:hover {
    color: #b02a37;
}

/* Progress Bars */
.progress {
    height: 1rem;
    border-radius: 0.5rem;
}

.progress-bar {
    border-radius: 0.5rem;
}

/* Charts */
.chart-container {
    position: relative;
    height: 400px;
    margin-bottom: 2rem;
}

/* Loading Spinner */
.loading-spinner {
    display: none;
    text-align: center;
    padding: 2rem;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* Footer */
footer {
    border-top: 1px solid #dee2e6;
    margin-top: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2.5rem;
    }
    
    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .prediction-value {
        font-size: 1.5rem;
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Property Type Badges */
.property-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 0.25rem;
    text-transform: uppercase;
}

.property-badge.physical {
    background-color: var(--info-color);
    color: white;
}

.property-badge.chemical {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.property-badge.biological {
    background-color: var(--success-color);
    color: white;
}

.property-badge.sensory {
    background-color: var(--secondary-color);
    color: white;
}

/* Formulation Type Colors */
.formulation-type.pharmaceutical {
    border-left: 4px solid var(--primary-color);
}

.formulation-type.cosmetic {
    border-left: 4px solid var(--success-color);
}

.formulation-type.food {
    border-left: 4px solid var(--warning-color);
}

.formulation-type.chemical {
    border-left: 4px solid var(--danger-color);
}

/* Ingredient Function Colors */
.function-active { color: var(--danger-color); }
.function-excipient { color: var(--primary-color); }
.function-stabilizer { color: var(--success-color); }
.function-emulsifier { color: var(--info-color); }
.function-preservative { color: var(--warning-color); }
.function-filler { color: var(--secondary-color); }
