"""
Data initialization utilities for the Formulation Science application
"""

from models.database_models import (
    FormulationType, Ingredient, PropertyType, Formulation,
    FormulationProperty, get_formulation_ingredients_table, db
)
import logging

logger = logging.getLogger(__name__)

def initialize_sample_data():
    """Initialize the database with sample data if it's empty"""
    
    try:
        # Check if data already exists
        if FormulationType.query.count() > 0:
            logger.info("Database already contains data, skipping initialization")
            return
        
        logger.info("Initializing database with sample data...")
        
        # Create formulation types
        formulation_types = [
            FormulationType(name='Tablet', description='Solid oral dosage form', category='pharmaceutical'),
            FormulationType(name='Capsule', description='Encapsulated oral dosage form', category='pharmaceutical'),
            FormulationType(name='Liquid', description='Liquid oral dosage form', category='pharmaceutical'),
            FormulationType(name='Cream', description='Semi-solid topical formulation', category='cosmetic'),
            FormulationType(name='Lotion', description='Liquid topical formulation', category='cosmetic'),
            FormulationType(name='Serum', description='Concentrated active formulation', category='cosmetic'),
            FormulationType(name='Emulsion', description='Oil-in-water or water-in-oil system', category='food'),
            FormulationType(name='Suspension', description='Solid particles in liquid medium', category='food'),
            FormulationType(name='Paint', description='Protective and decorative coating', category='chemical'),
            FormulationType(name='Adhesive', description='Bonding agent formulation', category='chemical')
        ]
        
        for ft in formulation_types:
            db.session.add(ft)
        
        # Create property types
        property_types = [
            PropertyType(name='viscosity', description='Resistance to flow', unit='cP', 
                        category='physical', measurement_method='Rheometer'),
            PropertyType(name='stability', description='Chemical and physical stability over time', 
                        unit='% remaining', category='physical', measurement_method='Accelerated stability testing'),
            PropertyType(name='solubility', description='Maximum concentration in solvent', 
                        unit='mg/mL', category='physical', measurement_method='Shake flask method'),
            PropertyType(name='bioavailability', description='Fraction of drug reaching systemic circulation', 
                        unit='%', category='biological', measurement_method='Pharmacokinetic study'),
            PropertyType(name='dissolution_rate', description='Rate of drug release from dosage form', 
                        unit='% in 30 min', category='physical', measurement_method='USP dissolution test'),
            PropertyType(name='texture_score', description='Sensory evaluation of texture', 
                        unit='1-10 scale', category='sensory', measurement_method='Panel evaluation'),
            PropertyType(name='hardness', description='Mechanical strength of solid dosage forms', 
                        unit='N', category='physical', measurement_method='Hardness tester'),
            PropertyType(name='spreadability', description='Ease of application for topical products', 
                        unit='mm', category='physical', measurement_method='Spreadability test'),
            PropertyType(name='ph', description='Acidity or alkalinity', 
                        unit='pH units', category='chemical', measurement_method='pH meter'),
            PropertyType(name='density', description='Mass per unit volume', 
                        unit='g/mL', category='physical', measurement_method='Pycnometer')
        ]
        
        for pt in property_types:
            db.session.add(pt)
        
        # Create sample ingredients
        ingredients = [
            # Pharmaceutical ingredients
            Ingredient(name='Microcrystalline Cellulose', cas_number='9004-34-6', 
                      molecular_formula='(C6H10O5)n', molecular_weight=162.14,
                      density=1.5, solubility_water=0, function_category='excipient',
                      safety_class='GRAS', regulatory_status='FDA approved'),
            
            Ingredient(name='Lactose Monohydrate', cas_number='64044-51-5',
                      molecular_formula='C12H22O11·H2O', molecular_weight=360.31,
                      density=1.54, solubility_water=200000, function_category='excipient',
                      safety_class='GRAS', regulatory_status='FDA approved'),
            
            Ingredient(name='Magnesium Stearate', cas_number='557-04-0',
                      molecular_formula='C36H70MgO4', molecular_weight=591.24,
                      density=1.03, solubility_water=0.003, function_category='lubricant',
                      safety_class='GRAS', regulatory_status='FDA approved'),
            
            Ingredient(name='Croscarmellose Sodium', cas_number='74811-65-7',
                      molecular_formula='C8H15NaO8', molecular_weight=266.19,
                      density=1.6, solubility_water=0, function_category='disintegrant',
                      safety_class='GRAS', regulatory_status='FDA approved'),
            
            Ingredient(name='Polyvinylpyrrolidone', cas_number='9003-39-8',
                      molecular_formula='(C6H9NO)n', molecular_weight=40000,
                      density=1.2, solubility_water=1000000, function_category='binder',
                      safety_class='GRAS', regulatory_status='FDA approved'),
            
            # Cosmetic ingredients
            Ingredient(name='Cetyl Alcohol', cas_number='36653-82-4',
                      molecular_formula='C16H34O', molecular_weight=242.44,
                      melting_point=49.3, density=0.811, solubility_water=0.001,
                      function_category='emulsifier', safety_class='cosmetic grade',
                      regulatory_status='INCI approved'),
            
            Ingredient(name='Glycerin', cas_number='56-81-5',
                      molecular_formula='C3H8O3', molecular_weight=92.09,
                      density=1.26, solubility_water=1000000, viscosity=1412,
                      function_category='humectant', safety_class='GRAS',
                      regulatory_status='FDA approved'),
            
            Ingredient(name='Dimethicone', cas_number='63148-62-9',
                      molecular_formula='(C2H6OSi)n', molecular_weight=7500,
                      density=0.97, solubility_water=0, viscosity=350,
                      function_category='emollient', safety_class='cosmetic grade',
                      regulatory_status='INCI approved'),
            
            Ingredient(name='Hyaluronic Acid', cas_number='9067-32-7',
                      molecular_formula='(C14H21NO11)n', molecular_weight=1000000,
                      density=1.5, solubility_water=1000000,
                      function_category='active', safety_class='cosmetic grade',
                      regulatory_status='INCI approved'),
            
            # Food ingredients
            Ingredient(name='Lecithin', cas_number='8002-43-5',
                      molecular_formula='C42H80NO8P', molecular_weight=758.07,
                      density=1.03, solubility_water=0.1,
                      function_category='emulsifier', safety_class='GRAS',
                      regulatory_status='FDA approved'),
            
            Ingredient(name='Xanthan Gum', cas_number='11138-66-2',
                      molecular_formula='(C35H49O29)n', molecular_weight=2000000,
                      density=1.6, solubility_water=1000000,
                      function_category='thickener', safety_class='GRAS',
                      regulatory_status='FDA approved'),
            
            Ingredient(name='Sodium Benzoate', cas_number='532-32-1',
                      molecular_formula='C7H5NaO2', molecular_weight=144.10,
                      density=1.44, solubility_water=630000,
                      function_category='preservative', safety_class='GRAS',
                      regulatory_status='FDA approved'),
            
            # Chemical ingredients
            Ingredient(name='Titanium Dioxide', cas_number='13463-67-7',
                      molecular_formula='TiO2', molecular_weight=79.87,
                      density=4.23, solubility_water=0,
                      function_category='pigment', safety_class='industrial grade',
                      regulatory_status='EPA approved'),
            
            Ingredient(name='Polyurethane Resin', cas_number='9009-54-5',
                      molecular_formula='(C3H6N2O2)n', molecular_weight=50000,
                      density=1.2, solubility_water=0,
                      function_category='polymer', safety_class='industrial grade',
                      regulatory_status='EPA approved'),
            
            Ingredient(name='Silica', cas_number='7631-86-9',
                      molecular_formula='SiO2', molecular_weight=60.08,
                      density=2.65, solubility_water=0.12,
                      function_category='filler', safety_class='industrial grade',
                      regulatory_status='EPA approved')
        ]
        
        for ingredient in ingredients:
            db.session.add(ingredient)
        
        # Commit the basic data
        db.session.commit()
        
        # Create sample formulations
        create_sample_formulations()
        
        logger.info("Sample data initialization completed successfully")
        
    except Exception as e:
        logger.error(f"Error initializing sample data: {str(e)}")
        db.session.rollback()
        raise

def create_sample_formulations():
    """Create sample formulations with ingredients"""
    
    try:
        # Get formulation types and ingredients
        tablet_type = FormulationType.query.filter_by(name='Tablet').first()
        cream_type = FormulationType.query.filter_by(name='Cream').first()
        
        mcc = Ingredient.query.filter_by(name='Microcrystalline Cellulose').first()
        lactose = Ingredient.query.filter_by(name='Lactose Monohydrate').first()
        mg_stearate = Ingredient.query.filter_by(name='Magnesium Stearate').first()
        
        cetyl_alcohol = Ingredient.query.filter_by(name='Cetyl Alcohol').first()
        glycerin = Ingredient.query.filter_by(name='Glycerin').first()
        dimethicone = Ingredient.query.filter_by(name='Dimethicone').first()
        
        # Sample tablet formulation
        if tablet_type and mcc and lactose and mg_stearate:
            tablet_formulation = Formulation(
                name='Basic Tablet Formulation',
                description='Simple immediate-release tablet with common excipients',
                type_id=tablet_type.id,
                temperature=25.0,
                ph_target=7.0,
                mixing_time=15.0,
                mixing_speed=300.0,
                created_by='System'
            )
            
            db.session.add(tablet_formulation)
            db.session.flush()
            
            # Add ingredients to tablet
            tablet_ingredients = [
                (mcc.id, 45.0, 'filler'),
                (lactose.id, 53.0, 'filler'),
                (mg_stearate.id, 2.0, 'lubricant')
            ]
            
            for ingredient_id, percentage, function in tablet_ingredients:
                stmt = formulation_ingredients.insert().values(
                    formulation_id=tablet_formulation.id,
                    ingredient_id=ingredient_id,
                    percentage=percentage,
                    function=function
                )
                db.session.execute(stmt)
        
        # Sample cream formulation
        if cream_type and cetyl_alcohol and glycerin and dimethicone:
            cream_formulation = Formulation(
                name='Basic Moisturizing Cream',
                description='Simple oil-in-water cream for skin moisturizing',
                type_id=cream_type.id,
                temperature=70.0,
                ph_target=6.5,
                mixing_time=45.0,
                mixing_speed=800.0,
                created_by='System'
            )
            
            db.session.add(cream_formulation)
            db.session.flush()
            
            # Add ingredients to cream
            cream_ingredients = [
                (cetyl_alcohol.id, 5.0, 'emulsifier'),
                (glycerin.id, 10.0, 'humectant'),
                (dimethicone.id, 3.0, 'emollient')
                # Note: Water would make up the remaining ~82%
            ]
            
            for ingredient_id, percentage, function in cream_ingredients:
                stmt = formulation_ingredients.insert().values(
                    formulation_id=cream_formulation.id,
                    ingredient_id=ingredient_id,
                    percentage=percentage,
                    function=function
                )
                db.session.execute(stmt)
        
        db.session.commit()
        logger.info("Sample formulations created successfully")
        
    except Exception as e:
        logger.error(f"Error creating sample formulations: {str(e)}")
        db.session.rollback()
        raise
