"""
Database models for the Formulation Science application
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from sqlalchemy import Column, Integer, String, Float, Text, DateTime, Boolean, ForeignKey, Table
from sqlalchemy.orm import relationship

# This will be set by the app
db = None

def get_formulation_ingredients_table():
    """Get the association table for formulations and ingredients"""
    if db is None:
        return None
    return Table('formulation_ingredients', db.metadata,
        Column('formulation_id', Integer, ForeignKey('formulation.id'), primary_key=True),
        Column('ingredient_id', Integer, ForeignKey('ingredient.id'), primary_key=True),
        Column('percentage', Float, nullable=False),  # Percentage of ingredient in formulation
        Column('function', String(100))  # Function of ingredient (e.g., active, excipient, stabilizer)
    )

class FormulationType(db.Model):
    """Types of formulations (pharmaceutical, cosmetic, food, chemical)"""
    __tablename__ = 'formulation_type'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50), nullable=False, unique=True)
    description = Column(Text)
    category = Column(String(50), nullable=False)  # pharmaceutical, cosmetic, food, chemical
    
    def __repr__(self):
        return f'<FormulationType {self.name}>'

class Ingredient(db.Model):
    """Individual ingredients with their properties"""
    __tablename__ = 'ingredient'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(200), nullable=False)
    cas_number = Column(String(20), unique=True)
    molecular_formula = Column(String(100))
    molecular_weight = Column(Float)
    
    # Physical properties
    melting_point = Column(Float)
    boiling_point = Column(Float)
    density = Column(Float)
    solubility_water = Column(Float)  # mg/L
    log_p = Column(Float)  # Partition coefficient
    
    # Chemical properties
    ph = Column(Float)
    pka = Column(Float)
    viscosity = Column(Float)
    
    # Safety and regulatory
    safety_class = Column(String(10))  # GRAS, pharmaceutical grade, etc.
    regulatory_status = Column(String(100))
    
    # Functional properties
    function_category = Column(String(100))  # active, excipient, stabilizer, etc.
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Ingredient {self.name}>'

class Formulation(db.Model):
    """Formulation recipes and compositions"""
    __tablename__ = 'formulation'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(200), nullable=False)
    description = Column(Text)
    
    # Formulation type
    type_id = Column(Integer, ForeignKey('formulation_type.id'), nullable=False)
    
    # Process parameters
    temperature = Column(Float)  # Processing temperature
    ph_target = Column(Float)
    mixing_time = Column(Float)  # minutes
    mixing_speed = Column(Float)  # RPM
    
    # Metadata
    created_by = Column(String(100))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships will be set up after table creation

    def get_ingredient_percentage(self, ingredient_id):
        """Get the percentage of a specific ingredient in this formulation"""
        formulation_ingredients = get_formulation_ingredients_table()
        if formulation_ingredients is None:
            return 0
        result = db.session.execute(
            formulation_ingredients.select().where(
                formulation_ingredients.c.formulation_id == self.id,
                formulation_ingredients.c.ingredient_id == ingredient_id
            )
        ).first()
        return result.percentage if result else 0
    
    def __repr__(self):
        return f'<Formulation {self.name}>'

class PropertyType(db.Model):
    """Types of properties that can be measured or predicted"""
    __tablename__ = 'property_type'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text)
    unit = Column(String(20))
    category = Column(String(50))  # physical, chemical, biological, sensory
    measurement_method = Column(String(200))
    
    def __repr__(self):
        return f'<PropertyType {self.name}>'

class FormulationProperty(db.Model):
    """Measured properties of formulations"""
    __tablename__ = 'formulation_property'
    
    id = Column(Integer, primary_key=True)
    formulation_id = Column(Integer, ForeignKey('formulation.id'), nullable=False)
    property_type_id = Column(Integer, ForeignKey('property_type.id'), nullable=False)
    
    value = Column(Float, nullable=False)
    uncertainty = Column(Float)  # Standard deviation or confidence interval
    measurement_conditions = Column(Text)  # Temperature, humidity, etc.
    
    # Metadata
    measured_by = Column(String(100))
    measured_at = Column(DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<FormulationProperty {self.formulation_id}-{self.property_type_id}: {self.value}>'

class PropertyPrediction(db.Model):
    """Predicted properties of formulations"""
    __tablename__ = 'property_prediction'
    
    id = Column(Integer, primary_key=True)
    formulation_id = Column(Integer, ForeignKey('formulation.id'), nullable=False)
    property_type_id = Column(Integer, ForeignKey('property_type.id'), nullable=False)
    
    predicted_value = Column(Float, nullable=False)
    confidence_interval_lower = Column(Float)
    confidence_interval_upper = Column(Float)
    model_used = Column(String(100))  # Random Forest, Neural Network, etc.
    model_version = Column(String(20))
    
    # Metadata
    predicted_at = Column(DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<PropertyPrediction {self.formulation_id}-{self.property_type_id}: {self.predicted_value}>'

def setup_relationships():
    """Set up relationships after database initialization"""
    formulation_ingredients = get_formulation_ingredients_table()
    if formulation_ingredients is not None:
        # Set up the many-to-many relationship
        Formulation.ingredients = relationship('Ingredient', secondary=formulation_ingredients, lazy='subquery',
                                             backref=db.backref('formulations', lazy=True))

        # Set up other relationships
        Formulation.properties = relationship('FormulationProperty', backref='formulation', lazy=True, cascade='all, delete-orphan')
        Formulation.predictions = relationship('PropertyPrediction', backref='formulation', lazy=True, cascade='all, delete-orphan')

        PropertyType.properties = relationship('FormulationProperty', backref='property_type', lazy=True)
        PropertyType.predictions = relationship('PropertyPrediction', backref='property_type', lazy=True)

        FormulationType.formulations = relationship('Formulation', backref='type', lazy=True)
