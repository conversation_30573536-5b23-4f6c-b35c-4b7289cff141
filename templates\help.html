{% extends "base.html" %}

{% block title %}Help - Formulation Science AI{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <h1 class="mb-4">
                <i class="fas fa-question-circle me-3"></i>
                Help & Documentation
            </h1>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="list-group" id="help-nav">
                        <a class="list-group-item list-group-item-action active" href="#getting-started">Getting Started</a>
                        <a class="list-group-item list-group-item-action" href="#creating-formulations">Creating Formulations</a>
                        <a class="list-group-item list-group-item-action" href="#predictions">Making Predictions</a>
                        <a class="list-group-item list-group-item-action" href="#ingredients">Managing Ingredients</a>
                        <a class="list-group-item list-group-item-action" href="#troubleshooting">Troubleshooting</a>
                    </div>
                </div>
                
                <div class="col-md-9">
                    <div id="getting-started" class="help-section">
                        <h2>Getting Started</h2>
                        <p>Welcome to Formulation Science AI! This guide will help you get started with the application.</p>
                        
                        <h4>Quick Start</h4>
                        <ol>
                            <li>Navigate to the <strong>Predict</strong> page from the main menu</li>
                            <li>Select your formulation type (pharmaceutical, cosmetic, food, or chemical)</li>
                            <li>Add ingredients with their percentages</li>
                            <li>Set process parameters (temperature, pH, mixing conditions)</li>
                            <li>Click "Predict Properties" to get results</li>
                        </ol>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>Tip:</strong> Start with the sample formulations in the database to understand how the system works.
                        </div>
                    </div>
                    
                    <div id="creating-formulations" class="help-section" style="display: none;">
                        <h2>Creating Formulations</h2>
                        <p>Learn how to create and manage formulations in the system.</p>
                        
                        <h4>Step-by-Step Guide</h4>
                        <ol>
                            <li><strong>Basic Information:</strong> Enter formulation name, type, and description</li>
                            <li><strong>Process Parameters:</strong> Set temperature, pH, mixing time, and speed</li>
                            <li><strong>Ingredients:</strong> Add ingredients with percentages and functions</li>
                            <li><strong>Validation:</strong> Ensure total percentage equals 100%</li>
                            <li><strong>Save:</strong> Save the formulation to the database</li>
                        </ol>
                        
                        <h4>Best Practices</h4>
                        <ul>
                            <li>Use descriptive names for easy identification</li>
                            <li>Include detailed descriptions of the formulation purpose</li>
                            <li>Specify ingredient functions (active, excipient, stabilizer, etc.)</li>
                            <li>Consider regulatory requirements for your industry</li>
                        </ul>
                    </div>
                    
                    <div id="predictions" class="help-section" style="display: none;">
                        <h2>Making Predictions</h2>
                        <p>Understand how to use the prediction engine effectively.</p>
                        
                        <h4>Prediction Models</h4>
                        <ul>
                            <li><strong>Random Forest:</strong> Default model providing robust predictions</li>
                            <li><strong>Neural Network:</strong> Advanced model for complex formulations</li>
                        </ul>
                        
                        <h4>Understanding Results</h4>
                        <ul>
                            <li><strong>Predicted Value:</strong> The most likely property value</li>
                            <li><strong>Confidence Interval:</strong> Range of uncertainty around the prediction</li>
                            <li><strong>Model Used:</strong> Which algorithm generated the prediction</li>
                        </ul>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Note:</strong> Predictions are estimates based on training data. Always validate with experimental testing.
                        </div>
                    </div>
                    
                    <div id="ingredients" class="help-section" style="display: none;">
                        <h2>Managing Ingredients</h2>
                        <p>Learn about the ingredient database and how to use it effectively.</p>
                        
                        <h4>Ingredient Information</h4>
                        <ul>
                            <li><strong>Basic Properties:</strong> Name, CAS number, molecular formula</li>
                            <li><strong>Physical Properties:</strong> Molecular weight, density, melting point</li>
                            <li><strong>Chemical Properties:</strong> LogP, solubility, pH, pKa</li>
                            <li><strong>Safety Data:</strong> Safety class, regulatory status</li>
                        </ul>
                        
                        <h4>Searching Ingredients</h4>
                        <ul>
                            <li>Use the search box to find ingredients by name</li>
                            <li>Filter by function category (active, excipient, etc.)</li>
                            <li>Browse by alphabetical order</li>
                        </ul>
                    </div>
                    
                    <div id="troubleshooting" class="help-section" style="display: none;">
                        <h2>Troubleshooting</h2>
                        <p>Common issues and their solutions.</p>
                        
                        <h4>Common Problems</h4>
                        
                        <h5>Prediction Errors</h5>
                        <ul>
                            <li><strong>Problem:</strong> "No ingredients selected"</li>
                            <li><strong>Solution:</strong> Add at least one ingredient with a percentage > 0</li>
                        </ul>
                        
                        <ul>
                            <li><strong>Problem:</strong> "Total percentage not 100%"</li>
                            <li><strong>Solution:</strong> Adjust ingredient percentages to sum to 100%</li>
                        </ul>
                        
                        <h5>Performance Issues</h5>
                        <ul>
                            <li><strong>Problem:</strong> Slow predictions</li>
                            <li><strong>Solution:</strong> Reduce number of ingredients or simplify formulation</li>
                        </ul>
                        
                        <h5>Data Issues</h5>
                        <ul>
                            <li><strong>Problem:</strong> Missing ingredient data</li>
                            <li><strong>Solution:</strong> Use ingredients with complete molecular descriptor data</li>
                        </ul>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-envelope me-2"></i>
                            <strong>Need More Help?</strong> Contact support for additional assistance with complex formulations.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Help navigation
document.addEventListener('DOMContentLoaded', function() {
    const navLinks = document.querySelectorAll('#help-nav .list-group-item');
    const sections = document.querySelectorAll('.help-section');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));
            
            // Add active class to clicked link
            this.classList.add('active');
            
            // Hide all sections
            sections.forEach(section => section.style.display = 'none');
            
            // Show target section
            const targetId = this.getAttribute('href').substring(1);
            const targetSection = document.getElementById(targetId);
            if (targetSection) {
                targetSection.style.display = 'block';
            }
        });
    });
});
</script>
{% endblock %}
