"""
Simplified database models for the Formulation Science application
"""

from datetime import datetime

def create_models(db):
    """Create all database models with the given SQLAlchemy instance"""
    
    # Association table for many-to-many relationship between formulations and ingredients
    formulation_ingredients = db.Table('formulation_ingredients',
        db.Column('formulation_id', db.<PERSON><PERSON>, db.<PERSON>('formulation.id'), primary_key=True),
        db.<PERSON>('ingredient_id', db.Integer, db.<PERSON>('ingredient.id'), primary_key=True),
        db.Column('percentage', db.Float, nullable=False),
        db.Column('function', db.String(100))
    )

    class FormulationType(db.Model):
        """Types of formulations (pharmaceutical, cosmetic, food, chemical)"""
        __tablename__ = 'formulation_type'
        
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(50), nullable=False, unique=True)
        description = db.Column(db.Text)
        category = db.Column(db.String(50), nullable=False)
        
        def __repr__(self):
            return f'<FormulationType {self.name}>'

    class Ingredient(db.Model):
        """Individual ingredients with their properties"""
        __tablename__ = 'ingredient'
        
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        cas_number = db.Column(db.String(20), unique=True)
        molecular_formula = db.Column(db.String(100))
        molecular_weight = db.Column(db.Float)
        
        # Physical properties
        melting_point = db.Column(db.Float)
        boiling_point = db.Column(db.Float)
        density = db.Column(db.Float)
        solubility_water = db.Column(db.Float)
        log_p = db.Column(db.Float)
        
        # Chemical properties
        ph = db.Column(db.Float)
        pka = db.Column(db.Float)
        viscosity = db.Column(db.Float)
        
        # Safety and regulatory
        safety_class = db.Column(db.String(10))
        regulatory_status = db.Column(db.String(100))
        function_category = db.Column(db.String(100))
        
        # Metadata
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        
        def __repr__(self):
            return f'<Ingredient {self.name}>'

    class Formulation(db.Model):
        """Formulation recipes and compositions"""
        __tablename__ = 'formulation'
        
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        description = db.Column(db.Text)
        
        # Formulation type
        type_id = db.Column(db.Integer, db.ForeignKey('formulation_type.id'), nullable=False)
        
        # Process parameters
        temperature = db.Column(db.Float)
        ph_target = db.Column(db.Float)
        mixing_time = db.Column(db.Float)
        mixing_speed = db.Column(db.Float)
        
        # Metadata
        created_by = db.Column(db.String(100))
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        
        # Relationships
        type = db.relationship('FormulationType', backref='formulations')
        ingredients = db.relationship('Ingredient', secondary=formulation_ingredients, lazy='subquery',
                                     backref=db.backref('formulations', lazy=True))
        
        def get_ingredient_percentage(self, ingredient_id):
            """Get the percentage of a specific ingredient in this formulation"""
            result = db.session.execute(
                formulation_ingredients.select().where(
                    formulation_ingredients.c.formulation_id == self.id,
                    formulation_ingredients.c.ingredient_id == ingredient_id
                )
            ).first()
            return result.percentage if result else 0
        
        def __repr__(self):
            return f'<Formulation {self.name}>'

    class PropertyType(db.Model):
        """Types of properties that can be measured or predicted"""
        __tablename__ = 'property_type'
        
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(100), nullable=False, unique=True)
        description = db.Column(db.Text)
        unit = db.Column(db.String(20))
        category = db.Column(db.String(50))
        measurement_method = db.Column(db.String(200))
        
        def __repr__(self):
            return f'<PropertyType {self.name}>'

    class FormulationProperty(db.Model):
        """Measured properties of formulations"""
        __tablename__ = 'formulation_property'
        
        id = db.Column(db.Integer, primary_key=True)
        formulation_id = db.Column(db.Integer, db.ForeignKey('formulation.id'), nullable=False)
        property_type_id = db.Column(db.Integer, db.ForeignKey('property_type.id'), nullable=False)
        
        value = db.Column(db.Float, nullable=False)
        uncertainty = db.Column(db.Float)
        measurement_conditions = db.Column(db.Text)
        
        # Metadata
        measured_by = db.Column(db.String(100))
        measured_at = db.Column(db.DateTime, default=datetime.utcnow)
        
        # Relationships
        formulation = db.relationship('Formulation', backref='properties')
        property_type = db.relationship('PropertyType', backref='properties')
        
        def __repr__(self):
            return f'<FormulationProperty {self.formulation_id}-{self.property_type_id}: {self.value}>'

    class PropertyPrediction(db.Model):
        """Predicted properties of formulations"""
        __tablename__ = 'property_prediction'
        
        id = db.Column(db.Integer, primary_key=True)
        formulation_id = db.Column(db.Integer, db.ForeignKey('formulation.id'), nullable=False)
        property_type_id = db.Column(db.Integer, db.ForeignKey('property_type.id'), nullable=False)
        
        predicted_value = db.Column(db.Float, nullable=False)
        confidence_interval_lower = db.Column(db.Float)
        confidence_interval_upper = db.Column(db.Float)
        model_used = db.Column(db.String(100))
        model_version = db.Column(db.String(20))
        
        # Metadata
        predicted_at = db.Column(db.DateTime, default=datetime.utcnow)
        
        # Relationships
        formulation = db.relationship('Formulation', backref='predictions')
        property_type = db.relationship('PropertyType', backref='predictions')
        
        def __repr__(self):
            return f'<PropertyPrediction {self.formulation_id}-{self.property_type_id}: {self.predicted_value}>'

    # Return all models and the association table
    return {
        'FormulationType': FormulationType,
        'Ingredient': Ingredient,
        'Formulation': Formulation,
        'PropertyType': PropertyType,
        'FormulationProperty': FormulationProperty,
        'PropertyPrediction': PropertyPrediction,
        'formulation_ingredients': formulation_ingredients
    }
