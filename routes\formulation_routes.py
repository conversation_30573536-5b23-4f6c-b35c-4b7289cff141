"""
Formulation-specific routes for creating and managing formulations
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from models.database_models import (
    Formulation, Ingredient, FormulationType, PropertyType,
    FormulationProperty, PropertyPrediction, get_formulation_ingredients_table, db
)
from models.prediction_models import FormulationPredictor
from datetime import datetime

formulation_bp = Blueprint('formulation', __name__)

# Initialize predictor
predictor = FormulationPredictor()

@formulation_bp.route('/create')
def create_formulation():
    """Create new formulation page"""
    formulation_types = FormulationType.query.all()
    ingredients = Ingredient.query.order_by(Ingredient.name).all()
    
    return render_template('formulation/create.html', 
                         formulation_types=formulation_types,
                         ingredients=ingredients)

@formulation_bp.route('/create', methods=['POST'])
def create_formulation_post():
    """Handle formulation creation"""
    try:
        # Get form data
        name = request.form.get('name')
        description = request.form.get('description', '')
        type_id = request.form.get('type_id', type=int)
        temperature = request.form.get('temperature', type=float)
        ph_target = request.form.get('ph_target', type=float)
        mixing_time = request.form.get('mixing_time', type=float)
        mixing_speed = request.form.get('mixing_speed', type=float)
        
        if not name or not type_id:
            flash('Name and formulation type are required', 'error')
            return redirect(url_for('formulation.create_formulation'))
        
        # Create formulation
        formulation = Formulation(
            name=name,
            description=description,
            type_id=type_id,
            temperature=temperature,
            ph_target=ph_target,
            mixing_time=mixing_time,
            mixing_speed=mixing_speed,
            created_by='Web User'
        )
        
        db.session.add(formulation)
        db.session.flush()  # Get the ID
        
        # Add ingredients
        ingredient_ids = request.form.getlist('ingredient_id')
        percentages = request.form.getlist('percentage')
        functions = request.form.getlist('function')
        
        total_percentage = 0
        for i, ingredient_id in enumerate(ingredient_ids):
            if ingredient_id and i < len(percentages) and percentages[i]:
                percentage = float(percentages[i])
                function = functions[i] if i < len(functions) else ''
                
                # Insert into association table
                formulation_ingredients = get_formulation_ingredients_table()
                if formulation_ingredients is not None:
                    stmt = formulation_ingredients.insert().values(
                        formulation_id=formulation.id,
                        ingredient_id=int(ingredient_id),
                        percentage=percentage,
                        function=function
                    )
                    db.session.execute(stmt)
                total_percentage += percentage
        
        # Validate total percentage
        if abs(total_percentage - 100.0) > 1.0:
            flash(f'Warning: Total percentage is {total_percentage:.1f}%, should be close to 100%', 'warning')
        
        db.session.commit()
        flash('Formulation created successfully!', 'success')
        
        return redirect(url_for('main.formulation_detail', formulation_id=formulation.id))
    
    except Exception as e:
        db.session.rollback()
        flash(f'Error creating formulation: {str(e)}', 'error')
        return redirect(url_for('formulation.create_formulation'))

@formulation_bp.route('/edit/<int:formulation_id>')
def edit_formulation(formulation_id):
    """Edit existing formulation page"""
    formulation = Formulation.query.get_or_404(formulation_id)
    formulation_types = FormulationType.query.all()
    ingredients = Ingredient.query.order_by(Ingredient.name).all()
    
    # Get current ingredient data
    current_ingredients = []
    for ingredient in formulation.ingredients:
        percentage = formulation.get_ingredient_percentage(ingredient.id)
        current_ingredients.append({
            'ingredient': ingredient,
            'percentage': percentage
        })
    
    return render_template('formulation/edit.html', 
                         formulation=formulation,
                         formulation_types=formulation_types,
                         ingredients=ingredients,
                         current_ingredients=current_ingredients)

@formulation_bp.route('/predict/<int:formulation_id>')
def predict_formulation(formulation_id):
    """Predict properties for an existing formulation"""
    formulation = Formulation.query.get_or_404(formulation_id)
    
    # Prepare formulation data
    ingredients_data = []
    for ingredient in formulation.ingredients:
        percentage = formulation.get_ingredient_percentage(ingredient.id)
        ingredients_data.append({
            'id': ingredient.id,
            'name': ingredient.name,
            'percentage': percentage,
            'molecular_weight': ingredient.molecular_weight,
            'log_p': ingredient.log_p,
            'solubility_water': ingredient.solubility_water,
            'melting_point': ingredient.melting_point,
            'density': ingredient.density
        })
    
    formulation_data = {
        'type': formulation.type.name.lower() if formulation.type else 'pharmaceutical',
        'ingredients': ingredients_data,
        'temperature': formulation.temperature or 25.0,
        'ph_target': formulation.ph_target or 7.0,
        'mixing_time': formulation.mixing_time or 30.0,
        'mixing_speed': formulation.mixing_speed or 500.0
    }
    
    try:
        # Get predictions
        predictions = predictor.predict_all_properties(formulation_data)
        
        # Save predictions to database
        for property_name, prediction_data in predictions.items():
            # Find or create property type
            property_type = PropertyType.query.filter_by(name=property_name).first()
            if not property_type:
                continue  # Skip if property type doesn't exist
            
            # Check if prediction already exists
            existing_prediction = PropertyPrediction.query.filter_by(
                formulation_id=formulation.id,
                property_type_id=property_type.id
            ).first()
            
            if existing_prediction:
                # Update existing prediction
                existing_prediction.predicted_value = prediction_data['predicted_value']
                existing_prediction.confidence_interval_lower = prediction_data['confidence_interval_lower']
                existing_prediction.confidence_interval_upper = prediction_data['confidence_interval_upper']
                existing_prediction.model_used = prediction_data['model_used']
                existing_prediction.predicted_at = datetime.utcnow()
            else:
                # Create new prediction
                new_prediction = PropertyPrediction(
                    formulation_id=formulation.id,
                    property_type_id=property_type.id,
                    predicted_value=prediction_data['predicted_value'],
                    confidence_interval_lower=prediction_data['confidence_interval_lower'],
                    confidence_interval_upper=prediction_data['confidence_interval_upper'],
                    model_used=prediction_data['model_used']
                )
                db.session.add(new_prediction)
        
        db.session.commit()
        flash('Properties predicted successfully!', 'success')
        
    except Exception as e:
        flash(f'Error predicting properties: {str(e)}', 'error')
        predictions = {}
    
    return render_template('formulation/predict.html', 
                         formulation=formulation,
                         predictions=predictions)

@formulation_bp.route('/compare')
def compare_formulations():
    """Compare multiple formulations page"""
    formulations = Formulation.query.all()
    return render_template('formulation/compare.html', formulations=formulations)

@formulation_bp.route('/optimize/<int:formulation_id>')
def optimize_formulation(formulation_id):
    """Formulation optimization page"""
    formulation = Formulation.query.get_or_404(formulation_id)
    property_types = PropertyType.query.all()
    
    return render_template('formulation/optimize.html', 
                         formulation=formulation,
                         property_types=property_types)

@formulation_bp.route('/batch-predict', methods=['POST'])
def batch_predict():
    """Predict properties for multiple formulations"""
    try:
        data = request.get_json()
        formulation_ids = data.get('formulation_ids', [])
        
        results = {}
        
        for formulation_id in formulation_ids:
            formulation = Formulation.query.get(formulation_id)
            if not formulation:
                continue
            
            # Prepare formulation data
            ingredients_data = []
            for ingredient in formulation.ingredients:
                percentage = formulation.get_ingredient_percentage(ingredient.id)
                ingredients_data.append({
                    'id': ingredient.id,
                    'name': ingredient.name,
                    'percentage': percentage,
                    'molecular_weight': ingredient.molecular_weight,
                    'log_p': ingredient.log_p,
                    'solubility_water': ingredient.solubility_water,
                    'melting_point': ingredient.melting_point,
                    'density': ingredient.density
                })
            
            formulation_data = {
                'type': formulation.type.name.lower() if formulation.type else 'pharmaceutical',
                'ingredients': ingredients_data,
                'temperature': formulation.temperature or 25.0,
                'ph_target': formulation.ph_target or 7.0,
                'mixing_time': formulation.mixing_time or 30.0,
                'mixing_speed': formulation.mixing_speed or 500.0
            }
            
            # Get predictions
            predictions = predictor.predict_all_properties(formulation_data)
            results[formulation_id] = {
                'name': formulation.name,
                'predictions': predictions
            }
        
        return jsonify({
            'success': True,
            'results': results
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@formulation_bp.route('/export/<int:formulation_id>')
def export_formulation(formulation_id):
    """Export formulation data"""
    formulation = Formulation.query.get_or_404(formulation_id)
    
    # Prepare export data
    export_data = {
        'formulation': {
            'name': formulation.name,
            'description': formulation.description,
            'type': formulation.type.name if formulation.type else None,
            'temperature': formulation.temperature,
            'ph_target': formulation.ph_target,
            'mixing_time': formulation.mixing_time,
            'mixing_speed': formulation.mixing_speed,
            'created_at': formulation.created_at.isoformat() if formulation.created_at else None
        },
        'ingredients': [],
        'properties': [],
        'predictions': []
    }
    
    # Add ingredients
    for ingredient in formulation.ingredients:
        percentage = formulation.get_ingredient_percentage(ingredient.id)
        export_data['ingredients'].append({
            'name': ingredient.name,
            'cas_number': ingredient.cas_number,
            'percentage': percentage,
            'function_category': ingredient.function_category
        })
    
    # Add measured properties
    for prop in formulation.properties:
        export_data['properties'].append({
            'property_type': prop.property_type.name,
            'value': prop.value,
            'unit': prop.property_type.unit,
            'uncertainty': prop.uncertainty
        })
    
    # Add predictions
    for pred in formulation.predictions:
        export_data['predictions'].append({
            'property_type': pred.property_type.name,
            'predicted_value': pred.predicted_value,
            'confidence_interval_lower': pred.confidence_interval_lower,
            'confidence_interval_upper': pred.confidence_interval_upper,
            'model_used': pred.model_used
        })
    
    return jsonify(export_data)
