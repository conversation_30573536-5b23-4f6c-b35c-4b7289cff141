#!/usr/bin/env python3
"""
Simple test script for the Formulation Science AI API
"""

import requests
import json

def test_prediction_api():
    """Test the prediction API endpoint"""
    
    url = "http://localhost:5000/api/predict"
    
    # Test data
    test_data = {
        "formulation_type": "pharmaceutical",
        "ingredients": [
            {
                "id": 1,
                "name": "Test Ingredient",
                "percentage": 50.0,
                "molecular_weight": 200.0,
                "log_p": 2.0,
                "solubility_water": 1000.0,
                "melting_point": 100.0,
                "density": 1.0
            }
        ],
        "temperature": 25.0,
        "ph_target": 7.0,
        "mixing_time": 30.0,
        "mixing_speed": 500.0
    }
    
    try:
        print("Testing Prediction API...")
        print(f"URL: {url}")
        print(f"Data: {json.dumps(test_data, indent=2)}")
        
        response = requests.post(url, json=test_data)
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Response Data: {json.dumps(result, indent=2)}")
            
            if result.get('success'):
                print("\n✅ API test successful!")
                predictions = result.get('predictions', {})
                for prop_name, prediction in predictions.items():
                    print(f"  {prop_name}: {prediction['predicted_value']:.2f} "
                          f"(CI: {prediction['confidence_interval_lower']:.2f} - "
                          f"{prediction['confidence_interval_upper']:.2f})")
            else:
                print(f"\n❌ API returned error: {result.get('error')}")
        else:
            print(f"\n❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - make sure the Flask app is running on localhost:5000")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_ingredients_api():
    """Test the ingredients API endpoint"""
    
    url = "http://localhost:5000/api/ingredients"
    
    try:
        print("\nTesting Ingredients API...")
        print(f"URL: {url}")
        
        response = requests.get(url, params={"limit": 5})
        
        print(f"\nResponse Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Response Data: {json.dumps(result, indent=2)}")
            
            if result.get('success'):
                print("\n✅ Ingredients API test successful!")
                ingredients = result.get('ingredients', [])
                print(f"Found {len(ingredients)} ingredients")
                for ingredient in ingredients[:3]:  # Show first 3
                    print(f"  - {ingredient['name']} (ID: {ingredient['id']})")
            else:
                print(f"\n❌ API returned error: {result.get('error')}")
        else:
            print(f"\n❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - make sure the Flask app is running on localhost:5000")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🧪 Formulation Science AI - API Test")
    print("=" * 50)
    
    test_prediction_api()
    test_ingredients_api()
    
    print("\n" + "=" * 50)
    print("Test completed!")
