{% extends "base.html" %}

{% block title %}Property Prediction - Formulation Science AI{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-brain me-3"></i>
                Property Prediction
            </h1>
            <p class="lead mb-4">
                Enter your formulation details below to predict key properties using advanced machine learning models.
            </p>
        </div>
    </div>

    <div class="row">
        <!-- Input Form -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-flask me-2"></i>
                        Formulation Details
                    </h5>
                </div>
                <div class="card-body">
                    <form id="prediction-form">
                        <!-- Formulation Type -->
                        <div class="mb-3">
                            <label for="formulation-type" class="form-label">Formulation Type</label>
                            <select class="form-select" id="formulation-type" required>
                                <option value="">Select formulation type...</option>
                                {% for type in formulation_types %}
                                <option value="{{ type.name.lower() }}">{{ type.name }} ({{ type.category }})</option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Process Parameters -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="temperature" class="form-label">Temperature (°C)</label>
                                <input type="number" class="form-control" id="temperature" 
                                       value="25" min="-50" max="200" step="0.1">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="ph-target" class="form-label">pH Target</label>
                                <input type="number" class="form-control" id="ph-target" 
                                       value="7.0" min="0" max="14" step="0.1">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="mixing-time" class="form-label">Mixing Time (min)</label>
                                <input type="number" class="form-control" id="mixing-time" 
                                       value="30" min="0" max="1440" step="1">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="mixing-speed" class="form-label">Mixing Speed (RPM)</label>
                                <input type="number" class="form-control" id="mixing-speed" 
                                       value="500" min="0" max="5000" step="10">
                            </div>
                        </div>

                        <!-- Ingredients Section -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label">Ingredients</label>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="add-ingredient-btn">
                                    <i class="fas fa-plus me-1"></i>Add Ingredient
                                </button>
                            </div>
                            
                            <div id="ingredients-container">
                                <!-- Initial ingredient row -->
                                <div class="ingredient-row" data-ingredient-id="1">
                                    <div class="row align-items-center">
                                        <div class="col-md-5">
                                            <label class="form-label">Ingredient</label>
                                            <select class="form-select ingredient-select" name="ingredient_id" required>
                                                <option value="">Select ingredient...</option>
                                                {% for ingredient in ingredients %}
                                                <option value="{{ ingredient.id }}" 
                                                        data-molecular-weight="{{ ingredient.molecular_weight or 200 }}"
                                                        data-log-p="{{ ingredient.log_p or 2 }}"
                                                        data-solubility-water="{{ ingredient.solubility_water or 1000 }}"
                                                        data-melting-point="{{ ingredient.melting_point or 100 }}"
                                                        data-density="{{ ingredient.density or 1 }}">
                                                    {{ ingredient.name }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">Percentage (%)</label>
                                            <input type="number" class="form-control percentage-input" 
                                                   name="percentage" min="0" max="100" step="0.01" required>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">Function</label>
                                            <select class="form-select" name="function">
                                                <option value="">Select function...</option>
                                                <option value="active">Active</option>
                                                <option value="excipient">Excipient</option>
                                                <option value="stabilizer">Stabilizer</option>
                                                <option value="emulsifier">Emulsifier</option>
                                                <option value="preservative">Preservative</option>
                                                <option value="filler">Filler</option>
                                                <option value="lubricant">Lubricant</option>
                                                <option value="disintegrant">Disintegrant</option>
                                                <option value="binder">Binder</option>
                                                <option value="coating">Coating</option>
                                            </select>
                                        </div>
                                        <div class="col-md-1">
                                            <label class="form-label">&nbsp;</label>
                                            <button type="button" class="btn btn-outline-danger w-100 remove-ingredient">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Total Percentage Display -->
                            <div class="mt-2 text-end">
                                <small class="text-muted">
                                    Total: <span id="total-percentage" class="fw-bold">0.00</span>%
                                </small>
                            </div>
                        </div>

                        <!-- Predict Button -->
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary btn-lg" id="predict-btn">
                                <i class="fas fa-brain me-2"></i>Predict Properties
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Results Panel -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Prediction Results
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Loading Spinner -->
                    <div id="loading-spinner" class="loading-spinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3 text-muted">Analyzing formulation and predicting properties...</p>
                    </div>

                    <!-- Results Container -->
                    <div id="prediction-results">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-chart-bar fa-3x mb-3"></i>
                            <p>Enter formulation details and click "Predict Properties" to see results.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Property Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Predictable Properties
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for property in property_types %}
                        <div class="col-md-6 mb-2">
                            <div class="d-flex align-items-center">
                                <span class="property-badge {{ property.category }}">{{ property.category }}</span>
                                <span class="ms-2 small">
                                    <strong>{{ property.name.replace('_', ' ').title() }}</strong>
                                    {% if property.unit %}({{ property.unit }}){% endif %}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Model Information -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-robot me-2"></i>
                        About Our Prediction Models
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Random Forest Models</h6>
                            <p class="small text-muted">
                                Ensemble learning method that combines multiple decision trees to provide 
                                robust predictions with confidence intervals based on tree variance.
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6>Neural Network Models</h6>
                            <p class="small text-muted">
                                Deep learning models trained on molecular descriptors and formulation 
                                parameters to capture complex non-linear relationships.
                            </p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Feature Engineering</h6>
                            <p class="small text-muted">
                                Models use molecular descriptors (MW, LogP, solubility), process parameters, 
                                and ingredient interactions for comprehensive property prediction.
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6>Confidence Intervals</h6>
                            <p class="small text-muted">
                                Uncertainty quantification provides confidence intervals to help assess 
                                prediction reliability and guide formulation decisions.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Initialize ingredient counter
let ingredientCounter = 1;

// Add ingredient options template for JavaScript
const ingredientOptionsTemplate = `
    {% for ingredient in ingredients %}
    <option value="{{ ingredient.id }}" 
            data-molecular-weight="{{ ingredient.molecular_weight or 200 }}"
            data-log-p="{{ ingredient.log_p or 2 }}"
            data-solubility-water="{{ ingredient.solubility_water or 1000 }}"
            data-melting-point="{{ ingredient.melting_point or 100 }}"
            data-density="{{ ingredient.density or 1 }}">
        {{ ingredient.name }}
    </option>
    {% endfor %}
`;

// Override the getIngredientOptions function
window.FormulationApp.getIngredientOptions = function() {
    return ingredientOptionsTemplate;
};
</script>
{% endblock %}
