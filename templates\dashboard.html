{% extends "base.html" %}

{% block title %}Dashboard - Formulation Science AI{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-chart-dashboard me-3"></i>
                Dashboard
            </h1>
        </div>
    </div>

    <!-- Statistics Overview -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="fas fa-vial fa-2x mb-2"></i>
                    <h4>{{ formulation_stats|length }}</h4>
                    <p class="mb-0">Formulation Types</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-atom fa-2x mb-2"></i>
                    <h4>{{ ingredient_categories|length }}</h4>
                    <p class="mb-0">Ingredient Categories</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                    <h4>6</h4>
                    <p class="mb-0">Prediction Models</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-brain fa-2x mb-2"></i>
                    <h4>10</h4>
                    <p class="mb-0">Property Types</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Formulations by Type</h5>
                </div>
                <div class="card-body">
                    <canvas id="formulation-chart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Ingredients by Category</h5>
                </div>
                <div class="card-body">
                    <canvas id="ingredient-chart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('main.predict') }}" class="btn btn-primary w-100">
                                <i class="fas fa-brain d-block mb-2"></i>
                                Predict Properties
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('formulation.create_formulation') }}" class="btn btn-success w-100">
                                <i class="fas fa-plus d-block mb-2"></i>
                                New Formulation
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('main.ingredients') }}" class="btn btn-info w-100">
                                <i class="fas fa-search d-block mb-2"></i>
                                Browse Ingredients
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('main.formulations') }}" class="btn btn-warning w-100">
                                <i class="fas fa-list d-block mb-2"></i>
                                View Formulations
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Create formulation types chart
const formulationCtx = document.getElementById('formulation-chart').getContext('2d');
new Chart(formulationCtx, {
    type: 'doughnut',
    data: {
        labels: [
            {% for stat in formulation_stats %}
            '{{ stat[0] }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for stat in formulation_stats %}
                {{ stat[1] }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: [
                '#0d6efd',
                '#198754',
                '#ffc107',
                '#dc3545',
                '#6f42c1',
                '#fd7e14',
                '#20c997',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Create ingredient categories chart
const ingredientCtx = document.getElementById('ingredient-chart').getContext('2d');
new Chart(ingredientCtx, {
    type: 'bar',
    data: {
        labels: [
            {% for category in ingredient_categories %}
            '{{ category[0] or "Unknown" }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: 'Count',
            data: [
                {% for category in ingredient_categories %}
                {{ category[1] }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: 'rgba(13, 110, 253, 0.8)',
            borderColor: 'rgba(13, 110, 253, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
{% endblock %}
