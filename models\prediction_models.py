"""
Machine Learning models for formulation property prediction
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import joblib
import os
from typing import Dict, List, Tuple, Optional
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FormulationPredictor:
    """Main prediction engine for formulation properties"""
    
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_columns = {}
        self.model_metrics = {}
        self.is_trained = False
        
        # Initialize models for different properties
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize ML models for different property types"""
        
        # Property-specific models
        property_models = {
            'viscosity': {
                'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
                'neural_network': MLPRegressor(hidden_layer_sizes=(100, 50), random_state=42, max_iter=1000)
            },
            'stability': {
                'random_forest': RandomForestRegressor(n_estimators=150, random_state=42),
                'neural_network': MLPRegressor(hidden_layer_sizes=(150, 75, 25), random_state=42, max_iter=1000)
            },
            'solubility': {
                'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
                'neural_network': MLPRegressor(hidden_layer_sizes=(100, 50), random_state=42, max_iter=1000)
            },
            'bioavailability': {
                'random_forest': RandomForestRegressor(n_estimators=200, random_state=42),
                'neural_network': MLPRegressor(hidden_layer_sizes=(200, 100, 50), random_state=42, max_iter=1500)
            },
            'dissolution_rate': {
                'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
                'neural_network': MLPRegressor(hidden_layer_sizes=(100, 50), random_state=42, max_iter=1000)
            },
            'texture_score': {
                'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
                'neural_network': MLPRegressor(hidden_layer_sizes=(80, 40), random_state=42, max_iter=1000)
            }
        }
        
        for property_name, models in property_models.items():
            self.models[property_name] = models
            self.scalers[property_name] = StandardScaler()
    
    def prepare_features(self, formulation_data: Dict) -> np.ndarray:
        """
        Prepare feature vector from formulation data
        
        Args:
            formulation_data: Dictionary containing formulation information
            
        Returns:
            Feature vector as numpy array
        """
        features = []
        
        # Ingredient-based features
        ingredients = formulation_data.get('ingredients', [])
        
        # Create ingredient percentage vector (top 50 most common ingredients)
        ingredient_percentages = np.zeros(50)
        for i, ingredient in enumerate(ingredients[:50]):
            if i < 50:
                ingredient_percentages[i] = ingredient.get('percentage', 0)
        
        features.extend(ingredient_percentages)
        
        # Molecular descriptors (averaged across ingredients)
        molecular_features = self._calculate_molecular_descriptors(ingredients)
        features.extend(molecular_features)
        
        # Process parameters
        process_features = [
            formulation_data.get('temperature', 25.0),
            formulation_data.get('ph_target', 7.0),
            formulation_data.get('mixing_time', 30.0),
            formulation_data.get('mixing_speed', 500.0)
        ]
        features.extend(process_features)
        
        # Formulation type encoding
        formulation_type = formulation_data.get('type', 'pharmaceutical')
        type_features = self._encode_formulation_type(formulation_type)
        features.extend(type_features)
        
        return np.array(features).reshape(1, -1)
    
    def _calculate_molecular_descriptors(self, ingredients: List[Dict]) -> List[float]:
        """Calculate averaged molecular descriptors for ingredients"""
        
        descriptors = []
        total_percentage = sum(ing.get('percentage', 0) for ing in ingredients)
        
        if total_percentage == 0:
            return [0.0] * 10  # Return zeros if no ingredients
        
        # Weighted averages of molecular properties
        avg_molecular_weight = sum(
            ing.get('molecular_weight', 200.0) * ing.get('percentage', 0) 
            for ing in ingredients
        ) / total_percentage
        
        avg_log_p = sum(
            ing.get('log_p', 2.0) * ing.get('percentage', 0) 
            for ing in ingredients
        ) / total_percentage
        
        avg_solubility = sum(
            ing.get('solubility_water', 1000.0) * ing.get('percentage', 0) 
            for ing in ingredients
        ) / total_percentage
        
        avg_melting_point = sum(
            ing.get('melting_point', 100.0) * ing.get('percentage', 0) 
            for ing in ingredients
        ) / total_percentage
        
        avg_density = sum(
            ing.get('density', 1.0) * ing.get('percentage', 0) 
            for ing in ingredients
        ) / total_percentage
        
        # Additional calculated descriptors
        polarity_index = sum(
            (1.0 if ing.get('log_p', 2.0) < 0 else 0.0) * ing.get('percentage', 0) 
            for ing in ingredients
        ) / total_percentage
        
        descriptors = [
            avg_molecular_weight,
            avg_log_p,
            avg_solubility,
            avg_melting_point,
            avg_density,
            polarity_index,
            len(ingredients),  # Number of ingredients
            total_percentage,  # Total percentage (should be ~100)
            np.std([ing.get('molecular_weight', 200.0) for ing in ingredients]),  # MW diversity
            np.std([ing.get('log_p', 2.0) for ing in ingredients])  # LogP diversity
        ]
        
        return descriptors
    
    def _encode_formulation_type(self, formulation_type: str) -> List[float]:
        """One-hot encode formulation type"""
        types = ['pharmaceutical', 'cosmetic', 'food', 'chemical']
        encoding = [1.0 if formulation_type == t else 0.0 for t in types]
        return encoding
    
    def predict_property(self, formulation_data: Dict, property_name: str, 
                        model_type: str = 'random_forest') -> Dict:
        """
        Predict a specific property for a formulation
        
        Args:
            formulation_data: Formulation information
            property_name: Name of property to predict
            model_type: Type of model to use ('random_forest' or 'neural_network')
            
        Returns:
            Dictionary with prediction results
        """
        
        if property_name not in self.models:
            raise ValueError(f"Property '{property_name}' not supported")
        
        if model_type not in self.models[property_name]:
            raise ValueError(f"Model type '{model_type}' not available for '{property_name}'")
        
        # Prepare features
        features = self.prepare_features(formulation_data)
        
        # Scale features if scaler is fitted
        if hasattr(self.scalers[property_name], 'mean_'):
            features_scaled = self.scalers[property_name].transform(features)
        else:
            features_scaled = features
        
        # Get model
        model = self.models[property_name][model_type]
        
        # Make prediction
        if hasattr(model, 'predict'):
            prediction = model.predict(features_scaled)[0]
            
            # Calculate confidence interval (simplified approach)
            if model_type == 'random_forest' and hasattr(model, 'estimators_'):
                # Use prediction variance from individual trees
                tree_predictions = [tree.predict(features_scaled)[0] for tree in model.estimators_]
                std_dev = np.std(tree_predictions)
                confidence_lower = prediction - 1.96 * std_dev
                confidence_upper = prediction + 1.96 * std_dev
            else:
                # Default confidence interval (±10%)
                std_dev = abs(prediction) * 0.1
                confidence_lower = prediction - std_dev
                confidence_upper = prediction + std_dev
        else:
            # Model not trained, return default prediction
            prediction = self._get_default_prediction(property_name, formulation_data)
            confidence_lower = prediction * 0.8
            confidence_upper = prediction * 1.2
        
        return {
            'predicted_value': float(prediction),
            'confidence_interval_lower': float(confidence_lower),
            'confidence_interval_upper': float(confidence_upper),
            'model_used': model_type,
            'property_name': property_name
        }
    
    def _get_default_prediction(self, property_name: str, formulation_data: Dict) -> float:
        """Generate default predictions based on formulation type and ingredients"""
        
        formulation_type = formulation_data.get('type', 'pharmaceutical')
        ingredients = formulation_data.get('ingredients', [])
        
        # Default values based on property and formulation type
        defaults = {
            'viscosity': {
                'pharmaceutical': 50.0,  # cP
                'cosmetic': 5000.0,
                'food': 1000.0,
                'chemical': 2000.0
            },
            'stability': {
                'pharmaceutical': 85.0,  # % remaining after 6 months
                'cosmetic': 90.0,
                'food': 80.0,
                'chemical': 95.0
            },
            'solubility': {
                'pharmaceutical': 10.0,  # mg/mL
                'cosmetic': 5.0,
                'food': 50.0,
                'chemical': 1.0
            },
            'bioavailability': {
                'pharmaceutical': 70.0,  # %
                'cosmetic': 30.0,
                'food': 85.0,
                'chemical': 10.0
            },
            'dissolution_rate': {
                'pharmaceutical': 80.0,  # % in 30 minutes
                'cosmetic': 95.0,
                'food': 90.0,
                'chemical': 50.0
            },
            'texture_score': {
                'pharmaceutical': 7.0,  # 1-10 scale
                'cosmetic': 8.5,
                'food': 7.5,
                'chemical': 6.0
            }
        }
        
        base_value = defaults.get(property_name, {}).get(formulation_type, 50.0)
        
        # Adjust based on number of ingredients (more ingredients = more complex = different properties)
        ingredient_factor = 1.0 + (len(ingredients) - 5) * 0.02
        
        return base_value * ingredient_factor
    
    def predict_all_properties(self, formulation_data: Dict) -> Dict:
        """Predict all available properties for a formulation"""
        
        results = {}
        
        for property_name in self.models.keys():
            try:
                # Try random forest first, fallback to neural network
                prediction = self.predict_property(formulation_data, property_name, 'random_forest')
                results[property_name] = prediction
            except Exception as e:
                logger.warning(f"Failed to predict {property_name}: {str(e)}")
                # Use default prediction
                default_value = self._get_default_prediction(property_name, formulation_data)
                results[property_name] = {
                    'predicted_value': default_value,
                    'confidence_interval_lower': default_value * 0.8,
                    'confidence_interval_upper': default_value * 1.2,
                    'model_used': 'default',
                    'property_name': property_name
                }
        
        return results
