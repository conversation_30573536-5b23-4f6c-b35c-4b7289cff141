{% extends "base.html" %}

{% block title %}Formulation Science AI - Home{% endblock %}

{% block content %}
<div class="container">
    <!-- Hero Section -->
    <div class="row mb-5">
        <div class="col-lg-8 mx-auto text-center">
            <h1 class="display-4 fw-bold text-primary mb-4">
                <i class="fas fa-flask me-3"></i>
                Formulation Science AI
            </h1>
            <p class="lead mb-4">
                Predict properties of pharmaceutical, cosmetic, food, and chemical formulations using 
                advanced machine learning algorithms and comprehensive ingredient databases.
            </p>
            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                <a href="{{ url_for('main.predict') }}" class="btn btn-primary btn-lg me-md-2">
                    <i class="fas fa-brain me-2"></i>Start Predicting
                </a>
                <a href="{{ url_for('formulation.create_formulation') }}" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>Create Formulation
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-5">
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-vial fa-3x mb-3"></i>
                    <h3 class="card-title">{{ stats.total_formulations }}</h3>
                    <p class="card-text">Total Formulations</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-atom fa-3x mb-3"></i>
                    <h3 class="card-title">{{ stats.total_ingredients }}</h3>
                    <p class="card-text">Ingredients Database</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-layer-group fa-3x mb-3"></i>
                    <h3 class="card-title">{{ stats.formulation_types }}</h3>
                    <p class="card-text">Formulation Types</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-3x mb-3"></i>
                    <h3 class="card-title">{{ stats.property_types }}</h3>
                    <p class="card-text">Predictable Properties</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="text-center mb-4">Key Features</h2>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-microscope fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">Multi-Domain Support</h5>
                    <p class="card-text">
                        Predict properties for pharmaceutical, cosmetic, food, and chemical formulations 
                        with specialized models for each domain.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-robot fa-3x text-success mb-3"></i>
                    <h5 class="card-title">AI-Powered Predictions</h5>
                    <p class="card-text">
                        Advanced machine learning models including Random Forest and Neural Networks 
                        provide accurate property predictions with confidence intervals.
                    </p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-database fa-3x text-info mb-3"></i>
                    <h5 class="card-title">Comprehensive Database</h5>
                    <p class="card-text">
                        Extensive ingredient database with molecular descriptors, safety data, 
                        and regulatory information for informed formulation decisions.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Formulations -->
    {% if recent_formulations %}
    <div class="row">
        <div class="col-12">
            <h3 class="mb-4">Recent Formulations</h3>
            <div class="row">
                {% for formulation in recent_formulations %}
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">
                                <a href="{{ url_for('main.formulation_detail', formulation_id=formulation.id) }}" 
                                   class="text-decoration-none">
                                    {{ formulation.name }}
                                </a>
                            </h6>
                            <p class="card-text text-muted small">
                                {{ formulation.description[:100] }}{% if formulation.description|length > 100 %}...{% endif %}
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-tag me-1"></i>
                                    {{ formulation.type.name if formulation.type else 'Unknown' }}
                                </small>
                                <small class="text-muted">
                                    {{ formulation.created_at.strftime('%Y-%m-%d') if formulation.created_at else '' }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            <div class="text-center mt-3">
                <a href="{{ url_for('main.formulations') }}" class="btn btn-outline-primary">
                    View All Formulations <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h4 class="card-title text-center mb-4">Quick Actions</h4>
                    <div class="row text-center">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('main.predict') }}" class="btn btn-outline-primary btn-lg w-100">
                                <i class="fas fa-brain d-block mb-2"></i>
                                Predict Properties
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('formulation.create_formulation') }}" class="btn btn-outline-success btn-lg w-100">
                                <i class="fas fa-plus d-block mb-2"></i>
                                New Formulation
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('main.ingredients') }}" class="btn btn-outline-info btn-lg w-100">
                                <i class="fas fa-search d-block mb-2"></i>
                                Browse Ingredients
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('main.dashboard') }}" class="btn btn-outline-warning btn-lg w-100">
                                <i class="fas fa-chart-dashboard d-block mb-2"></i>
                                View Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
