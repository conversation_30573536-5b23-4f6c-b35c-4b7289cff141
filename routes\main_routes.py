"""
Main web routes for the Formulation Science application
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash
from models.database_models import FormulationType, Ingredient, Formulation, PropertyType, db

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """Main landing page"""
    # Get statistics for dashboard
    stats = {
        'total_formulations': Formulation.query.count(),
        'total_ingredients': Ingredient.query.count(),
        'formulation_types': FormulationType.query.count(),
        'property_types': PropertyType.query.count()
    }
    
    # Get recent formulations
    recent_formulations = Formulation.query.order_by(Formulation.created_at.desc()).limit(5).all()
    
    return render_template('index.html', stats=stats, recent_formulations=recent_formulations)

@main_bp.route('/dashboard')
def dashboard():
    """Main dashboard with overview"""
    # Get formulation statistics by type
    formulation_stats = db.session.query(
        FormulationType.name,
        db.func.count(Formulation.id).label('count')
    ).join(Formulation).group_by(FormulationType.name).all()
    
    # Get ingredient categories
    ingredient_categories = db.session.query(
        Ingredient.function_category,
        db.func.count(Ingredient.id).label('count')
    ).group_by(Ingredient.function_category).all()
    
    return render_template('dashboard.html', 
                         formulation_stats=formulation_stats,
                         ingredient_categories=ingredient_categories)

@main_bp.route('/ingredients')
def ingredients():
    """Ingredients library page"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category = request.args.get('category', '')
    
    query = Ingredient.query
    
    if search:
        query = query.filter(Ingredient.name.contains(search))
    
    if category:
        query = query.filter(Ingredient.function_category == category)
    
    ingredients = query.paginate(
        page=page, per_page=20, error_out=False
    )
    
    # Get available categories for filter
    categories = db.session.query(Ingredient.function_category).distinct().all()
    categories = [cat[0] for cat in categories if cat[0]]
    
    return render_template('ingredients.html', 
                         ingredients=ingredients, 
                         categories=categories,
                         search=search,
                         selected_category=category)

@main_bp.route('/ingredients/<int:ingredient_id>')
def ingredient_detail(ingredient_id):
    """Individual ingredient detail page"""
    ingredient = Ingredient.query.get_or_404(ingredient_id)
    
    # Get formulations using this ingredient
    formulations = ingredient.formulations
    
    return render_template('ingredient_detail.html', 
                         ingredient=ingredient,
                         formulations=formulations)

@main_bp.route('/formulations')
def formulations():
    """Formulations library page"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    type_filter = request.args.get('type', '')
    
    query = Formulation.query
    
    if search:
        query = query.filter(Formulation.name.contains(search))
    
    if type_filter:
        query = query.join(FormulationType).filter(FormulationType.name == type_filter)
    
    formulations = query.paginate(
        page=page, per_page=15, error_out=False
    )
    
    # Get available types for filter
    types = FormulationType.query.all()
    
    return render_template('formulations.html', 
                         formulations=formulations, 
                         types=types,
                         search=search,
                         selected_type=type_filter)

@main_bp.route('/formulations/<int:formulation_id>')
def formulation_detail(formulation_id):
    """Individual formulation detail page"""
    formulation = Formulation.query.get_or_404(formulation_id)
    
    # Get ingredient percentages
    ingredient_data = []
    for ingredient in formulation.ingredients:
        percentage = formulation.get_ingredient_percentage(ingredient.id)
        ingredient_data.append({
            'ingredient': ingredient,
            'percentage': percentage
        })
    
    return render_template('formulation_detail.html', 
                         formulation=formulation,
                         ingredient_data=ingredient_data)

@main_bp.route('/predict')
def predict():
    """Prediction interface page"""
    # Get available formulation types and ingredients for the form
    formulation_types = FormulationType.query.all()
    ingredients = Ingredient.query.order_by(Ingredient.name).all()
    property_types = PropertyType.query.all()
    
    return render_template('predict.html', 
                         formulation_types=formulation_types,
                         ingredients=ingredients,
                         property_types=property_types)

@main_bp.route('/about')
def about():
    """About page with information about the application"""
    return render_template('about.html')

@main_bp.route('/help')
def help():
    """Help and documentation page"""
    return render_template('help.html')
