"""
API routes for the Formulation Science application
"""

from flask import Blueprint, request, jsonify
from models.database_models import (
    Formulation, Ingredient, FormulationType, PropertyType,
    FormulationProperty, PropertyPrediction, get_formulation_ingredients_table, db
)
from models.prediction_models import FormulationPredictor
import json
from datetime import datetime

api_bp = Blueprint('api', __name__)

# Initialize predictor
predictor = FormulationPredictor()

@api_bp.route('/predict', methods=['POST'])
def predict_properties():
    """API endpoint for property prediction"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Validate required fields
        required_fields = ['formulation_type', 'ingredients']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Prepare formulation data for prediction
        formulation_data = {
            'type': data['formulation_type'],
            'ingredients': data['ingredients'],
            'temperature': data.get('temperature', 25.0),
            'ph_target': data.get('ph_target', 7.0),
            'mixing_time': data.get('mixing_time', 30.0),
            'mixing_speed': data.get('mixing_speed', 500.0)
        }
        
        # Get predictions
        if 'property_name' in data:
            # Predict single property
            property_name = data['property_name']
            model_type = data.get('model_type', 'random_forest')
            
            prediction = predictor.predict_property(
                formulation_data, property_name, model_type
            )
            
            return jsonify({
                'success': True,
                'prediction': prediction
            })
        else:
            # Predict all properties
            predictions = predictor.predict_all_properties(formulation_data)
            
            return jsonify({
                'success': True,
                'predictions': predictions
            })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/ingredients', methods=['GET'])
def get_ingredients():
    """Get ingredients list with optional filtering"""
    try:
        search = request.args.get('search', '')
        category = request.args.get('category', '')
        limit = request.args.get('limit', 100, type=int)
        
        query = Ingredient.query
        
        if search:
            query = query.filter(Ingredient.name.contains(search))
        
        if category:
            query = query.filter(Ingredient.function_category == category)
        
        ingredients = query.limit(limit).all()
        
        result = []
        for ingredient in ingredients:
            result.append({
                'id': ingredient.id,
                'name': ingredient.name,
                'cas_number': ingredient.cas_number,
                'molecular_formula': ingredient.molecular_formula,
                'molecular_weight': ingredient.molecular_weight,
                'function_category': ingredient.function_category,
                'solubility_water': ingredient.solubility_water,
                'log_p': ingredient.log_p,
                'safety_class': ingredient.safety_class
            })
        
        return jsonify({
            'success': True,
            'ingredients': result
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/ingredients/<int:ingredient_id>', methods=['GET'])
def get_ingredient(ingredient_id):
    """Get detailed information about a specific ingredient"""
    try:
        ingredient = Ingredient.query.get_or_404(ingredient_id)
        
        result = {
            'id': ingredient.id,
            'name': ingredient.name,
            'cas_number': ingredient.cas_number,
            'molecular_formula': ingredient.molecular_formula,
            'molecular_weight': ingredient.molecular_weight,
            'melting_point': ingredient.melting_point,
            'boiling_point': ingredient.boiling_point,
            'density': ingredient.density,
            'solubility_water': ingredient.solubility_water,
            'log_p': ingredient.log_p,
            'ph': ingredient.ph,
            'pka': ingredient.pka,
            'viscosity': ingredient.viscosity,
            'safety_class': ingredient.safety_class,
            'regulatory_status': ingredient.regulatory_status,
            'function_category': ingredient.function_category,
            'created_at': ingredient.created_at.isoformat() if ingredient.created_at else None
        }
        
        return jsonify({
            'success': True,
            'ingredient': result
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/formulations', methods=['GET'])
def get_formulations():
    """Get formulations list with optional filtering"""
    try:
        search = request.args.get('search', '')
        type_filter = request.args.get('type', '')
        limit = request.args.get('limit', 50, type=int)
        
        query = Formulation.query
        
        if search:
            query = query.filter(Formulation.name.contains(search))
        
        if type_filter:
            query = query.join(FormulationType).filter(FormulationType.name == type_filter)
        
        formulations = query.limit(limit).all()
        
        result = []
        for formulation in formulations:
            result.append({
                'id': formulation.id,
                'name': formulation.name,
                'description': formulation.description,
                'type': formulation.type.name if formulation.type else None,
                'created_at': formulation.created_at.isoformat() if formulation.created_at else None,
                'ingredient_count': len(formulation.ingredients)
            })
        
        return jsonify({
            'success': True,
            'formulations': result
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/formulations/<int:formulation_id>', methods=['GET'])
def get_formulation(formulation_id):
    """Get detailed information about a specific formulation"""
    try:
        formulation = Formulation.query.get_or_404(formulation_id)
        
        # Get ingredients with percentages
        ingredients = []
        for ingredient in formulation.ingredients:
            percentage = formulation.get_ingredient_percentage(ingredient.id)
            ingredients.append({
                'id': ingredient.id,
                'name': ingredient.name,
                'percentage': percentage,
                'function_category': ingredient.function_category
            })
        
        # Get properties
        properties = []
        for prop in formulation.properties:
            properties.append({
                'property_type': prop.property_type.name,
                'value': prop.value,
                'unit': prop.property_type.unit,
                'uncertainty': prop.uncertainty,
                'measured_at': prop.measured_at.isoformat() if prop.measured_at else None
            })
        
        # Get predictions
        predictions = []
        for pred in formulation.predictions:
            predictions.append({
                'property_type': pred.property_type.name,
                'predicted_value': pred.predicted_value,
                'confidence_interval_lower': pred.confidence_interval_lower,
                'confidence_interval_upper': pred.confidence_interval_upper,
                'model_used': pred.model_used,
                'predicted_at': pred.predicted_at.isoformat() if pred.predicted_at else None
            })
        
        result = {
            'id': formulation.id,
            'name': formulation.name,
            'description': formulation.description,
            'type': formulation.type.name if formulation.type else None,
            'temperature': formulation.temperature,
            'ph_target': formulation.ph_target,
            'mixing_time': formulation.mixing_time,
            'mixing_speed': formulation.mixing_speed,
            'created_at': formulation.created_at.isoformat() if formulation.created_at else None,
            'ingredients': ingredients,
            'properties': properties,
            'predictions': predictions
        }
        
        return jsonify({
            'success': True,
            'formulation': result
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/formulation-types', methods=['GET'])
def get_formulation_types():
    """Get available formulation types"""
    try:
        types = FormulationType.query.all()
        
        result = []
        for type_obj in types:
            result.append({
                'id': type_obj.id,
                'name': type_obj.name,
                'description': type_obj.description,
                'category': type_obj.category
            })
        
        return jsonify({
            'success': True,
            'formulation_types': result
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/property-types', methods=['GET'])
def get_property_types():
    """Get available property types"""
    try:
        properties = PropertyType.query.all()
        
        result = []
        for prop in properties:
            result.append({
                'id': prop.id,
                'name': prop.name,
                'description': prop.description,
                'unit': prop.unit,
                'category': prop.category,
                'measurement_method': prop.measurement_method
            })
        
        return jsonify({
            'success': True,
            'property_types': result
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/save-formulation', methods=['POST'])
def save_formulation():
    """Save a new formulation to the database"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Create new formulation
        formulation = Formulation(
            name=data.get('name', 'Untitled Formulation'),
            description=data.get('description', ''),
            type_id=data.get('type_id'),
            temperature=data.get('temperature'),
            ph_target=data.get('ph_target'),
            mixing_time=data.get('mixing_time'),
            mixing_speed=data.get('mixing_speed'),
            created_by=data.get('created_by', 'API User')
        )
        
        db.session.add(formulation)
        db.session.flush()  # Get the ID
        
        # Add ingredients
        for ingredient_data in data.get('ingredients', []):
            ingredient_id = ingredient_data.get('ingredient_id')
            percentage = ingredient_data.get('percentage', 0)
            function = ingredient_data.get('function', '')
            
            # Insert into association table
            formulation_ingredients = get_formulation_ingredients_table()
            if formulation_ingredients is not None:
                stmt = formulation_ingredients.insert().values(
                    formulation_id=formulation.id,
                    ingredient_id=ingredient_id,
                    percentage=percentage,
                    function=function
                )
                db.session.execute(stmt)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'formulation_id': formulation.id,
            'message': 'Formulation saved successfully'
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
