"""
Advanced pharmaceutical formulation types and their characterization properties
Focus on solubility enhancement and specialized drug delivery systems
"""

# Advanced Formulation Types
ADVANCED_FORMULATION_TYPES = [
    {
        'name': 'Solid Dispersion',
        'category': 'pharmaceutical',
        'description': 'Amorphous or crystalline drug dispersed in polymer matrix for solubility enhancement',
        'primary_purpose': 'solubility_enhancement',
        'manufacturing_methods': ['hot_melt_extrusion', 'spray_drying', 'solvent_evaporation']
    },
    {
        'name': 'Nanoparticles',
        'category': 'pharmaceutical',
        'description': 'Drug-loaded nanoparticles for targeted delivery and solubility enhancement',
        'primary_purpose': 'targeted_delivery',
        'manufacturing_methods': ['nanoprecipitation', 'emulsification', 'ionic_gelation']
    },
    {
        'name': 'Liposomes',
        'category': 'pharmaceutical',
        'description': 'Phospholipid vesicles for drug encapsulation and delivery',
        'primary_purpose': 'targeted_delivery',
        'manufacturing_methods': ['thin_film_hydration', 'reverse_phase_evaporation', 'microfluidics']
    },
    {
        'name': 'Hydrogel',
        'category': 'pharmaceutical',
        'description': 'Three-dimensional polymer network for sustained drug release',
        'primary_purpose': 'sustained_release',
        'manufacturing_methods': ['physical_crosslinking', 'chemical_crosslinking', 'ionic_crosslinking']
    },
    {
        'name': 'Oleogel',
        'category': 'pharmaceutical',
        'description': 'Oil-based gel system for lipophilic drug delivery',
        'primary_purpose': 'lipophilic_delivery',
        'manufacturing_methods': ['direct_dispersion', 'phase_inversion', 'cooling_gelation']
    },
    {
        'name': 'Organogel',
        'category': 'pharmaceutical',
        'description': 'Organic solvent-based gel for topical and transdermal delivery',
        'primary_purpose': 'topical_delivery',
        'manufacturing_methods': ['heating_cooling', 'solvent_addition', 'pH_adjustment']
    },
    {
        'name': 'Transdermal Patch',
        'category': 'pharmaceutical',
        'description': 'Adhesive patch for controlled transdermal drug delivery',
        'primary_purpose': 'transdermal_delivery',
        'manufacturing_methods': ['matrix_type', 'reservoir_type', 'drug_in_adhesive']
    },
    {
        'name': 'Self-Emulsifying Drug Delivery System',
        'category': 'pharmaceutical',
        'description': 'Lipid-based system that forms emulsion upon dilution',
        'primary_purpose': 'solubility_enhancement',
        'manufacturing_methods': ['simple_mixing', 'phase_titration', 'ternary_diagram']
    },
    {
        'name': 'Cyclodextrin Inclusion Complex',
        'category': 'pharmaceutical',
        'description': 'Drug-cyclodextrin complex for solubility enhancement',
        'primary_purpose': 'solubility_enhancement',
        'manufacturing_methods': ['kneading', 'coprecipitation', 'freeze_drying']
    },
    {
        'name': 'Microemulsion',
        'category': 'pharmaceutical',
        'description': 'Thermodynamically stable dispersion for enhanced bioavailability',
        'primary_purpose': 'solubility_enhancement',
        'manufacturing_methods': ['spontaneous_emulsification', 'phase_inversion', 'ultrasonication']
    }
]

# Comprehensive Characterization Properties for Each Formulation Type
CHARACTERIZATION_PROPERTIES = {
    'solid_dispersion': [
        {
            'name': 'glass_transition_temperature',
            'description': 'Temperature at which polymer transitions from glassy to rubbery state',
            'unit': '°C',
            'category': 'thermal',
            'measurement_method': 'DSC',
            'typical_range': [40, 200],
            'importance': 'critical'
        },
        {
            'name': 'crystallinity_index',
            'description': 'Degree of crystallinity in the dispersion',
            'unit': '%',
            'category': 'physical',
            'measurement_method': 'XRD',
            'typical_range': [0, 100],
            'importance': 'critical'
        },
        {
            'name': 'dissolution_rate',
            'description': 'Rate of drug release from solid dispersion',
            'unit': '% in 30 min',
            'category': 'biopharmaceutical',
            'measurement_method': 'USP dissolution',
            'typical_range': [20, 100],
            'importance': 'critical'
        },
        {
            'name': 'supersaturation_ratio',
            'description': 'Ratio of achieved concentration to equilibrium solubility',
            'unit': 'fold',
            'category': 'biopharmaceutical',
            'measurement_method': 'UV spectroscopy',
            'typical_range': [2, 50],
            'importance': 'high'
        },
        {
            'name': 'physical_stability',
            'description': 'Maintenance of amorphous state over time',
            'unit': '% crystalline after 6 months',
            'category': 'stability',
            'measurement_method': 'XRD',
            'typical_range': [0, 20],
            'importance': 'critical'
        }
    ],
    
    'nanoparticles': [
        {
            'name': 'particle_size',
            'description': 'Mean particle diameter',
            'unit': 'nm',
            'category': 'physical',
            'measurement_method': 'DLS',
            'typical_range': [50, 500],
            'importance': 'critical'
        },
        {
            'name': 'polydispersity_index',
            'description': 'Measure of particle size distribution',
            'unit': 'dimensionless',
            'category': 'physical',
            'measurement_method': 'DLS',
            'typical_range': [0.1, 0.5],
            'importance': 'high'
        },
        {
            'name': 'zeta_potential',
            'description': 'Surface charge of nanoparticles',
            'unit': 'mV',
            'category': 'physical',
            'measurement_method': 'Electrophoretic light scattering',
            'typical_range': [-50, 50],
            'importance': 'high'
        },
        {
            'name': 'encapsulation_efficiency',
            'description': 'Percentage of drug encapsulated',
            'unit': '%',
            'category': 'biopharmaceutical',
            'measurement_method': 'HPLC',
            'typical_range': [50, 95],
            'importance': 'critical'
        },
        {
            'name': 'drug_loading',
            'description': 'Amount of drug per unit weight of nanoparticles',
            'unit': '% w/w',
            'category': 'biopharmaceutical',
            'measurement_method': 'HPLC',
            'typical_range': [1, 30],
            'importance': 'high'
        },
        {
            'name': 'in_vitro_release',
            'description': 'Drug release profile in physiological conditions',
            'unit': '% released in 24h',
            'category': 'biopharmaceutical',
            'measurement_method': 'Dialysis bag method',
            'typical_range': [20, 100],
            'importance': 'critical'
        }
    ],
    
    'hydrogel': [
        {
            'name': 'gel_strength',
            'description': 'Mechanical strength of the gel',
            'unit': 'Pa',
            'category': 'mechanical',
            'measurement_method': 'Rheometer',
            'typical_range': [100, 10000],
            'importance': 'high'
        },
        {
            'name': 'swelling_ratio',
            'description': 'Degree of water uptake',
            'unit': 'g water/g dry gel',
            'category': 'physical',
            'measurement_method': 'Gravimetric',
            'typical_range': [5, 100],
            'importance': 'high'
        },
        {
            'name': 'gelation_time',
            'description': 'Time required for gel formation',
            'unit': 'minutes',
            'category': 'physical',
            'measurement_method': 'Visual observation',
            'typical_range': [1, 60],
            'importance': 'medium'
        },
        {
            'name': 'drug_release_kinetics',
            'description': 'Pattern of drug release over time',
            'unit': '% released in 8h',
            'category': 'biopharmaceutical',
            'measurement_method': 'Franz diffusion cell',
            'typical_range': [30, 90],
            'importance': 'critical'
        },
        {
            'name': 'mucoadhesive_strength',
            'description': 'Adhesion to mucosal surfaces',
            'unit': 'N',
            'category': 'biopharmaceutical',
            'measurement_method': 'Texture analyzer',
            'typical_range': [0.1, 5.0],
            'importance': 'high'
        }
    ],
    
    'transdermal_patch': [
        {
            'name': 'flux',
            'description': 'Rate of drug permeation through skin',
            'unit': 'μg/cm²/h',
            'category': 'biopharmaceutical',
            'measurement_method': 'Franz diffusion cell',
            'typical_range': [1, 100],
            'importance': 'critical'
        },
        {
            'name': 'lag_time',
            'description': 'Time before steady-state permeation',
            'unit': 'hours',
            'category': 'biopharmaceutical',
            'measurement_method': 'Franz diffusion cell',
            'typical_range': [0.5, 8],
            'importance': 'high'
        },
        {
            'name': 'adhesion_strength',
            'description': 'Force required to remove patch',
            'unit': 'N/25mm',
            'category': 'mechanical',
            'measurement_method': 'Peel test',
            'typical_range': [2, 15],
            'importance': 'high'
        },
        {
            'name': 'tack_strength',
            'description': 'Initial adhesion to skin',
            'unit': 'N',
            'category': 'mechanical',
            'measurement_method': 'Probe tack test',
            'typical_range': [5, 25],
            'importance': 'medium'
        },
        {
            'name': 'shear_strength',
            'description': 'Resistance to sliding under load',
            'unit': 'hours',
            'category': 'mechanical',
            'measurement_method': 'Shear test',
            'typical_range': [12, 72],
            'importance': 'high'
        },
        {
            'name': 'moisture_vapor_transmission',
            'description': 'Breathability of the patch',
            'unit': 'g/m²/24h',
            'category': 'physical',
            'measurement_method': 'MVTR test',
            'typical_range': [100, 2000],
            'importance': 'medium'
        }
    ],
    
    'oleogel': [
        {
            'name': 'oil_binding_capacity',
            'description': 'Amount of oil that can be immobilized',
            'unit': 'g oil/g gelator',
            'category': 'physical',
            'measurement_method': 'Gravimetric',
            'typical_range': [10, 200],
            'importance': 'high'
        },
        {
            'name': 'thermal_stability',
            'description': 'Temperature at which gel melts',
            'unit': '°C',
            'category': 'thermal',
            'measurement_method': 'DSC',
            'typical_range': [40, 100],
            'importance': 'high'
        },
        {
            'name': 'rheological_properties',
            'description': 'Viscoelastic behavior of oleogel',
            'unit': 'Pa',
            'category': 'mechanical',
            'measurement_method': 'Rheometer',
            'typical_range': [1000, 50000],
            'importance': 'high'
        },
        {
            'name': 'drug_solubility',
            'description': 'Solubility of drug in oleogel matrix',
            'unit': 'mg/g',
            'category': 'biopharmaceutical',
            'measurement_method': 'HPLC',
            'typical_range': [1, 100],
            'importance': 'critical'
        }
    ],
    
    'microemulsion': [
        {
            'name': 'droplet_size',
            'description': 'Size of dispersed phase droplets',
            'unit': 'nm',
            'category': 'physical',
            'measurement_method': 'DLS',
            'typical_range': [10, 100],
            'importance': 'critical'
        },
        {
            'name': 'thermodynamic_stability',
            'description': 'Stability under stress conditions',
            'unit': 'pass/fail',
            'category': 'stability',
            'measurement_method': 'Centrifugation/heating',
            'typical_range': [0, 1],
            'importance': 'critical'
        },
        {
            'name': 'drug_solubilization',
            'description': 'Enhancement of drug solubility',
            'unit': 'fold increase',
            'category': 'biopharmaceutical',
            'measurement_method': 'UV spectroscopy',
            'typical_range': [5, 1000],
            'importance': 'critical'
        },
        {
            'name': 'viscosity',
            'description': 'Flow properties of microemulsion',
            'unit': 'cP',
            'category': 'physical',
            'measurement_method': 'Viscometer',
            'typical_range': [10, 500],
            'importance': 'medium'
        }
    ]
}

# Solubility Enhancement Mechanisms
SOLUBILITY_MECHANISMS = [
    'micelle_formation',
    'inclusion_complex',
    'amorphous_dispersion',
    'cosolvent_effect',
    'particle_size_reduction',
    'salt_formation',
    'prodrug_approach',
    'lipid_based_delivery',
    'supersaturation',
    'pH_modification'
]
