{% extends "base.html" %}

{% block title %}Create Formulation - Formulation Science AI{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-plus me-3"></i>
                Create New Formulation
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('main.formulations') }}">Formulations</a></li>
                    <li class="breadcrumb-item active">Create New</li>
                </ol>
            </nav>
        </div>
    </div>

    <form method="POST" action="{{ url_for('formulation.create_formulation_post') }}">
        <div class="row">
            <!-- Basic Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Basic Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="name" class="form-label">Formulation Name *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="type_id" class="form-label">Type *</label>
                                <select class="form-select" id="type_id" name="type_id" required>
                                    <option value="">Select type...</option>
                                    {% for type in formulation_types %}
                                    <option value="{{ type.id }}">{{ type.name }} ({{ type.category }})</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Process Parameters -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Process Parameters</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="temperature" class="form-label">Temperature (°C)</label>
                                <input type="number" class="form-control" id="temperature" name="temperature" 
                                       value="25" min="-50" max="200" step="0.1">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="ph_target" class="form-label">pH Target</label>
                                <input type="number" class="form-control" id="ph_target" name="ph_target" 
                                       value="7.0" min="0" max="14" step="0.1">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="mixing_time" class="form-label">Mixing Time (minutes)</label>
                                <input type="number" class="form-control" id="mixing_time" name="mixing_time" 
                                       value="30" min="0" max="1440" step="1">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="mixing_speed" class="form-label">Mixing Speed (RPM)</label>
                                <input type="number" class="form-control" id="mixing_speed" name="mixing_speed" 
                                       value="500" min="0" max="5000" step="10">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ingredients -->
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Ingredients</h5>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="add-ingredient-btn">
                                <i class="fas fa-plus me-1"></i>Add Ingredient
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="ingredients-container">
                            <!-- Initial ingredient row -->
                            <div class="ingredient-row mb-3" data-ingredient-id="1">
                                <div class="row align-items-end">
                                    <div class="col-md-4">
                                        <label class="form-label">Ingredient</label>
                                        <select class="form-select ingredient-select" name="ingredient_id">
                                            <option value="">Select ingredient...</option>
                                            {% for ingredient in ingredients %}
                                            <option value="{{ ingredient.id }}">{{ ingredient.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Percentage (%)</label>
                                        <input type="number" class="form-control percentage-input" 
                                               name="percentage" min="0" max="100" step="0.01">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Function</label>
                                        <select class="form-select" name="function">
                                            <option value="">Select function...</option>
                                            <option value="active">Active</option>
                                            <option value="excipient">Excipient</option>
                                            <option value="stabilizer">Stabilizer</option>
                                            <option value="emulsifier">Emulsifier</option>
                                            <option value="preservative">Preservative</option>
                                            <option value="filler">Filler</option>
                                            <option value="lubricant">Lubricant</option>
                                            <option value="disintegrant">Disintegrant</option>
                                            <option value="binder">Binder</option>
                                            <option value="coating">Coating</option>
                                        </select>
                                    </div>
                                    <div class="col-md-1">
                                        <button type="button" class="btn btn-outline-danger remove-ingredient">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Total Percentage Display -->
                        <div class="text-end">
                            <strong>Total: <span id="total-percentage" class="text-primary">0.00</span>%</strong>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Actions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-save me-2"></i>Save Formulation
                            </button>
                            <a href="{{ url_for('main.formulations') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Tips -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>Tips
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled small">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Total percentage should equal 100%
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Include at least one active ingredient
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Consider regulatory requirements
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Process parameters affect properties
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Initialize ingredient counter
let ingredientCounter = 1;

// Ingredient options template
const ingredientOptionsTemplate = `
    <option value="">Select ingredient...</option>
    {% for ingredient in ingredients %}
    <option value="{{ ingredient.id }}">{{ ingredient.name }}</option>
    {% endfor %}
`;

// Override the getIngredientOptions function
window.FormulationApp.getIngredientOptions = function() {
    return ingredientOptionsTemplate;
};

// Initialize the form
document.addEventListener('DOMContentLoaded', function() {
    // Update total percentage on page load
    updateTotalPercentage();
});
</script>
{% endblock %}
