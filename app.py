"""
Formulation Science Web Application
Main Flask application entry point
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_cors import CORS
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'formulation-science-secret-key-2024')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///formulation_science.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
migrate = Migrate(app, db)
CORS(app)

# Set the db instance in models
import models.database_models as db_models
db_models.db = db

# Import routes
from routes.main_routes import main_bp
from routes.api_routes import api_bp
from routes.formulation_routes import formulation_bp

# Register blueprints
app.register_blueprint(main_bp)
app.register_blueprint(api_bp, url_prefix='/api')
app.register_blueprint(formulation_bp, url_prefix='/formulation')

@app.route('/')
def index():
    """Main landing page"""
    return render_template('index.html')

@app.errorhandler(404)
def not_found_error(error):
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return render_template('errors/500.html'), 500

if __name__ == '__main__':
    with app.app_context():
        # Import models to ensure they are registered
        from models.database_models import *
        from models.database_models import setup_relationships

        # Create database tables
        db.create_all()

        # Set up relationships
        setup_relationships()

        # Initialize sample data if database is empty
        from utils.data_initialization import initialize_sample_data
        initialize_sample_data()

    app.run(debug=True, host='0.0.0.0', port=5000)
