"""
Formulation Science Web Application
Main Flask application entry point
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_cors import CORS
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'formulation-science-secret-key-2024')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///formulation_science.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize extensions
db = SQLAlchemy(app)
migrate = Migrate(app, db)
CORS(app)

# Create models
from models.simple_models import create_models
models = create_models(db)

# Store models and db in app config for access in routes
app.config['MODELS'] = models
app.config['DB'] = db

# Import routes after models are created
from routes.simple_routes import main_bp, api_bp, formulation_bp

# Register blueprints
app.register_blueprint(main_bp)
app.register_blueprint(api_bp, url_prefix='/api')
app.register_blueprint(formulation_bp, url_prefix='/formulation')

# Routes are handled by blueprints

@app.errorhandler(404)
def not_found_error(error):
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return render_template('errors/500.html'), 500

if __name__ == '__main__':
    with app.app_context():
        # Create database tables
        db.create_all()

        # Initialize sample data if database is empty
        from utils.simple_data_init import initialize_sample_data
        initialize_sample_data(db, models)

    app.run(debug=True, host='0.0.0.0', port=5000)
