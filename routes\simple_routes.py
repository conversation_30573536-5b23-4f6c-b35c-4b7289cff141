"""
Simple routes for the Formulation Science application
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask import current_app
import logging

logger = logging.getLogger(__name__)

main_bp = Blueprint('main', __name__)
api_bp = Blueprint('api', __name__)
formulation_bp = Blueprint('formulation', __name__)

def get_models():
    """Get models from the current app context"""
    return current_app.config.get('MODELS', {})

def get_db():
    """Get database from the current app context"""
    return current_app.config.get('DB')

@main_bp.route('/')
def index():
    """Main landing page"""
    try:
        models = get_models()
        if not models:
            return render_template('index.html', stats={}, recent_formulations=[])
        
        FormulationType = models.get('FormulationType')
        Ingredient = models.get('Ingredient')
        Formulation = models.get('Formulation')
        PropertyType = models.get('PropertyType')
        
        # Get statistics for dashboard
        stats = {
            'total_formulations': Formulation.query.count() if Formulation else 0,
            'total_ingredients': Ingredient.query.count() if Ingredient else 0,
            'formulation_types': FormulationType.query.count() if FormulationType else 0,
            'property_types': PropertyType.query.count() if PropertyType else 0
        }
        
        # Get recent formulations
        recent_formulations = []
        if Formulation:
            recent_formulations = Formulation.query.order_by(Formulation.created_at.desc()).limit(5).all()
        
        return render_template('index.html', stats=stats, recent_formulations=recent_formulations)
    except Exception as e:
        logger.error(f"Error in index route: {str(e)}")
        return render_template('index.html', stats={}, recent_formulations=[])

@main_bp.route('/dashboard')
def dashboard():
    """Main dashboard with overview"""
    try:
        models = get_models()
        db = get_db()
        
        if not models or not db:
            return render_template('dashboard.html', formulation_stats=[], ingredient_categories=[])
        
        FormulationType = models.get('FormulationType')
        Ingredient = models.get('Ingredient')
        Formulation = models.get('Formulation')
        
        # Get formulation statistics by type
        formulation_stats = []
        if FormulationType and Formulation:
            formulation_stats = db.session.query(
                FormulationType.name,
                db.func.count(Formulation.id).label('count')
            ).join(Formulation).group_by(FormulationType.name).all()
        
        # Get ingredient categories
        ingredient_categories = []
        if Ingredient:
            ingredient_categories = db.session.query(
                Ingredient.function_category,
                db.func.count(Ingredient.id).label('count')
            ).group_by(Ingredient.function_category).all()
        
        return render_template('dashboard.html', 
                             formulation_stats=formulation_stats,
                             ingredient_categories=ingredient_categories)
    except Exception as e:
        logger.error(f"Error in dashboard route: {str(e)}")
        return render_template('dashboard.html', formulation_stats=[], ingredient_categories=[])

@main_bp.route('/ingredients')
def ingredients():
    """Ingredients library page"""
    try:
        models = get_models()
        if not models:
            return render_template('ingredients.html', ingredients=None, categories=[])
        
        Ingredient = models.get('Ingredient')
        if not Ingredient:
            return render_template('ingredients.html', ingredients=None, categories=[])
        
        page = request.args.get('page', 1, type=int)
        search = request.args.get('search', '')
        category = request.args.get('category', '')
        
        query = Ingredient.query
        
        if search:
            query = query.filter(Ingredient.name.contains(search))
        
        if category:
            query = query.filter(Ingredient.function_category == category)
        
        ingredients = query.paginate(
            page=page, per_page=20, error_out=False
        )
        
        # Get available categories for filter
        categories = get_db().session.query(Ingredient.function_category).distinct().all()
        categories = [cat[0] for cat in categories if cat[0]]
        
        return render_template('ingredients.html', 
                             ingredients=ingredients, 
                             categories=categories,
                             search=search,
                             selected_category=category)
    except Exception as e:
        logger.error(f"Error in ingredients route: {str(e)}")
        return render_template('ingredients.html', ingredients=None, categories=[])

@main_bp.route('/formulations')
def formulations():
    """Formulations library page"""
    try:
        models = get_models()
        if not models:
            return render_template('formulations.html', formulations=None, types=[])
        
        Formulation = models.get('Formulation')
        FormulationType = models.get('FormulationType')
        
        if not Formulation:
            return render_template('formulations.html', formulations=None, types=[])
        
        page = request.args.get('page', 1, type=int)
        search = request.args.get('search', '')
        type_filter = request.args.get('type', '')
        
        query = Formulation.query
        
        if search:
            query = query.filter(Formulation.name.contains(search))
        
        if type_filter and FormulationType:
            query = query.join(FormulationType).filter(FormulationType.name == type_filter)
        
        formulations = query.paginate(
            page=page, per_page=15, error_out=False
        )
        
        # Get available types for filter
        types = FormulationType.query.all() if FormulationType else []
        
        return render_template('formulations.html', 
                             formulations=formulations, 
                             types=types,
                             search=search,
                             selected_type=type_filter)
    except Exception as e:
        logger.error(f"Error in formulations route: {str(e)}")
        return render_template('formulations.html', formulations=None, types=[])

@main_bp.route('/predict')
def predict():
    """Prediction interface page"""
    try:
        models = get_models()
        if not models:
            return render_template('predict.html', formulation_types=[], ingredients=[], property_types=[])
        
        FormulationType = models.get('FormulationType')
        Ingredient = models.get('Ingredient')
        PropertyType = models.get('PropertyType')
        
        # Get available formulation types and ingredients for the form
        formulation_types = FormulationType.query.all() if FormulationType else []
        ingredients = Ingredient.query.order_by(Ingredient.name).all() if Ingredient else []
        property_types = PropertyType.query.all() if PropertyType else []
        
        return render_template('predict.html', 
                             formulation_types=formulation_types,
                             ingredients=ingredients,
                             property_types=property_types)
    except Exception as e:
        logger.error(f"Error in predict route: {str(e)}")
        return render_template('predict.html', formulation_types=[], ingredients=[], property_types=[])

@main_bp.route('/about')
def about():
    """About page with information about the application"""
    return render_template('about.html')

@main_bp.route('/help')
def help():
    """Help and documentation page"""
    return render_template('help.html')

@main_bp.route('/formulations/<int:formulation_id>')
def formulation_detail(formulation_id):
    """Individual formulation detail page"""
    try:
        models = get_models()
        if not models:
            flash('Application not properly initialized', 'error')
            return redirect(url_for('main.formulations'))

        Formulation = models.get('Formulation')
        if not Formulation:
            flash('Formulation model not available', 'error')
            return redirect(url_for('main.formulations'))

        formulation = Formulation.query.get_or_404(formulation_id)

        # Get ingredient percentages
        ingredient_data = []
        for ingredient in formulation.ingredients:
            percentage = formulation.get_ingredient_percentage(ingredient.id)
            ingredient_data.append({
                'ingredient': ingredient,
                'percentage': percentage
            })

        return render_template('formulation_detail.html',
                             formulation=formulation,
                             ingredient_data=ingredient_data)
    except Exception as e:
        logger.error(f"Error in formulation_detail route: {str(e)}")
        flash('Error loading formulation details', 'error')
        return redirect(url_for('main.formulations'))

# Formulation Blueprint Routes
@formulation_bp.route('/create')
def create_formulation():
    """Create new formulation page"""
    try:
        models = get_models()
        if not models:
            flash('Application not properly initialized', 'error')
            return redirect(url_for('main.index'))

        FormulationType = models.get('FormulationType')
        Ingredient = models.get('Ingredient')

        formulation_types = FormulationType.query.all() if FormulationType else []
        ingredients = Ingredient.query.order_by(Ingredient.name).all() if Ingredient else []

        return render_template('formulation/create.html',
                             formulation_types=formulation_types,
                             ingredients=ingredients)
    except Exception as e:
        logger.error(f"Error in create_formulation route: {str(e)}")
        flash('Error loading formulation creation page', 'error')
        return redirect(url_for('main.index'))

# API Routes
@api_bp.route('/predict', methods=['POST'])
def predict_properties():
    """API endpoint for property prediction"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Validate required fields
        required_fields = ['formulation_type', 'ingredients']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Import prediction models
        from models.prediction_models import FormulationPredictor
        predictor = FormulationPredictor()
        
        # Prepare formulation data for prediction
        formulation_data = {
            'type': data['formulation_type'],
            'ingredients': data['ingredients'],
            'temperature': data.get('temperature', 25.0),
            'ph_target': data.get('ph_target', 7.0),
            'mixing_time': data.get('mixing_time', 30.0),
            'mixing_speed': data.get('mixing_speed', 500.0)
        }
        
        # Get predictions
        if 'property_name' in data:
            # Predict single property
            property_name = data['property_name']
            model_type = data.get('model_type', 'random_forest')
            
            prediction = predictor.predict_property(
                formulation_data, property_name, model_type
            )
            
            return jsonify({
                'success': True,
                'prediction': prediction
            })
        else:
            # Predict all properties
            predictions = predictor.predict_all_properties(formulation_data)
            
            return jsonify({
                'success': True,
                'predictions': predictions
            })
    
    except Exception as e:
        logger.error(f"Error in predict API: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/ingredients', methods=['GET'])
def get_ingredients():
    """Get ingredients list with optional filtering"""
    try:
        models = get_models()
        if not models:
            return jsonify({'success': False, 'error': 'Models not available'}), 500
        
        Ingredient = models.get('Ingredient')
        if not Ingredient:
            return jsonify({'success': False, 'error': 'Ingredient model not available'}), 500
        
        search = request.args.get('search', '')
        category = request.args.get('category', '')
        limit = request.args.get('limit', 100, type=int)
        
        query = Ingredient.query
        
        if search:
            query = query.filter(Ingredient.name.contains(search))
        
        if category:
            query = query.filter(Ingredient.function_category == category)
        
        ingredients = query.limit(limit).all()
        
        result = []
        for ingredient in ingredients:
            result.append({
                'id': ingredient.id,
                'name': ingredient.name,
                'cas_number': ingredient.cas_number,
                'molecular_formula': ingredient.molecular_formula,
                'molecular_weight': ingredient.molecular_weight,
                'function_category': ingredient.function_category,
                'solubility_water': ingredient.solubility_water,
                'log_p': ingredient.log_p,
                'safety_class': ingredient.safety_class
            })
        
        return jsonify({
            'success': True,
            'ingredients': result
        })
    
    except Exception as e:
        logger.error(f"Error in get_ingredients API: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
