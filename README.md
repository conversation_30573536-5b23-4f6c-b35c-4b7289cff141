# Formulation Science AI

A comprehensive web application for predicting properties of various types of formulations using advanced machine learning algorithms.

## Features

- **Multi-Domain Support**: Pharmaceutical, cosmetic, food, and chemical formulations
- **AI-Powered Predictions**: Random Forest and Neural Network models
- **Comprehensive Database**: Extensive ingredient library with molecular descriptors
- **Interactive Interface**: Modern, responsive web interface
- **Property Prediction**: Viscosity, stability, solubility, bioavailability, and more
- **Confidence Intervals**: Uncertainty quantification for all predictions

## Technology Stack

### Backend
- Python Flask framework
- SQLAlchemy ORM for database management
- scikit-learn for machine learning models
- TensorFlow for neural networks
- SQLite database (easily configurable for other databases)

### Frontend
- HTML5, CSS3, JavaScript
- Bootstrap 5 for responsive design
- Chart.js for data visualization
- Font Awesome icons

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd formulation_ai
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

5. **Initialize database**
   ```bash
   python app.py
   ```

## Usage

1. **Start the application**
   ```bash
   python app.py
   ```

2. **Access the web interface**
   Open your browser and navigate to `http://localhost:5000`

3. **Create formulations**
   - Go to "Formulations" > "Create New"
   - Enter formulation details and ingredients
   - Save the formulation

4. **Make predictions**
   - Go to "Predict" page
   - Select formulation type and enter ingredients
   - Click "Predict Properties" to get results

## API Endpoints

### Prediction API
```
POST /api/predict
Content-Type: application/json

{
  "formulation_type": "pharmaceutical",
  "ingredients": [
    {
      "id": 1,
      "name": "Microcrystalline Cellulose",
      "percentage": 45.0,
      "molecular_weight": 162.14,
      "log_p": -3.0,
      "solubility_water": 0
    }
  ],
  "temperature": 25.0,
  "ph_target": 7.0,
  "mixing_time": 30.0,
  "mixing_speed": 500.0
}
```

### Get Ingredients
```
GET /api/ingredients?search=cellulose&category=excipient&limit=50
```

### Get Formulations
```
GET /api/formulations?type=pharmaceutical&limit=20
```

## Supported Formulation Types

### Pharmaceutical
- Tablets
- Capsules
- Liquids
- Properties: Dissolution rate, bioavailability, stability, hardness

### Cosmetic
- Creams
- Lotions
- Serums
- Properties: Viscosity, texture score, spreadability, stability

### Food
- Emulsions
- Suspensions
- Properties: Stability, texture, viscosity, pH

### Chemical
- Paints
- Adhesives
- Properties: Viscosity, stability, density

## Predictable Properties

1. **Viscosity** (cP) - Resistance to flow
2. **Stability** (% remaining) - Chemical and physical stability over time
3. **Solubility** (mg/mL) - Maximum concentration in solvent
4. **Bioavailability** (%) - Fraction reaching systemic circulation
5. **Dissolution Rate** (% in 30 min) - Rate of drug release
6. **Texture Score** (1-10 scale) - Sensory evaluation

## Machine Learning Models

### Random Forest
- Ensemble method with multiple decision trees
- Provides confidence intervals based on tree variance
- Robust to overfitting and handles missing data well

### Neural Networks
- Multi-layer perceptron with configurable architecture
- Captures complex non-linear relationships
- Trained on molecular descriptors and process parameters

### Feature Engineering
- Molecular descriptors (MW, LogP, solubility, etc.)
- Process parameters (temperature, pH, mixing conditions)
- Ingredient interactions and ratios
- Formulation type encoding

## Database Schema

### Core Tables
- `formulation_type` - Types of formulations
- `ingredient` - Ingredient library with properties
- `formulation` - Formulation recipes
- `property_type` - Types of predictable properties
- `formulation_property` - Measured properties
- `property_prediction` - Predicted properties

### Relationships
- Many-to-many between formulations and ingredients
- One-to-many from formulations to properties and predictions

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions, issues, or feature requests, please open an issue on the GitHub repository.

## Acknowledgments

- Built with Flask and modern web technologies
- Machine learning models based on formulation science research
- Inspired by the need for data-driven formulation development
