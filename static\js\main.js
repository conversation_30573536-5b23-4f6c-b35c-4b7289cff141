// Main JavaScript for Formulation Science AI

// Global variables
let ingredientCounter = 0;
let predictionChart = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize ingredient management
    initializeIngredientManagement();
    
    // Initialize prediction functionality
    initializePredictionForms();
    
    // Initialize charts
    initializeCharts();
}

// Ingredient Management Functions
function initializeIngredientManagement() {
    const addIngredientBtn = document.getElementById('add-ingredient-btn');
    if (addIngredientBtn) {
        addIngredientBtn.addEventListener('click', addIngredientRow);
    }
    
    // Initialize existing ingredient rows
    document.querySelectorAll('.ingredient-row').forEach(function(row) {
        initializeIngredientRow(row);
    });
}

function addIngredientRow() {
    const container = document.getElementById('ingredients-container');
    if (!container) return;
    
    ingredientCounter++;
    
    const rowHtml = `
        <div class="ingredient-row" data-ingredient-id="${ingredientCounter}">
            <div class="row align-items-center">
                <div class="col-md-5">
                    <label class="form-label">Ingredient</label>
                    <select class="form-select ingredient-select" name="ingredient_id" required>
                        <option value="">Select ingredient...</option>
                        ${getIngredientOptions()}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Percentage (%)</label>
                    <input type="number" class="form-control percentage-input" 
                           name="percentage" min="0" max="100" step="0.01" required>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Function</label>
                    <select class="form-select" name="function">
                        <option value="">Select function...</option>
                        <option value="active">Active</option>
                        <option value="excipient">Excipient</option>
                        <option value="stabilizer">Stabilizer</option>
                        <option value="emulsifier">Emulsifier</option>
                        <option value="preservative">Preservative</option>
                        <option value="filler">Filler</option>
                        <option value="lubricant">Lubricant</option>
                        <option value="disintegrant">Disintegrant</option>
                        <option value="binder">Binder</option>
                        <option value="coating">Coating</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <button type="button" class="btn btn-outline-danger w-100 remove-ingredient">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', rowHtml);
    
    // Initialize the new row
    const newRow = container.lastElementChild;
    initializeIngredientRow(newRow);
    
    // Update total percentage
    updateTotalPercentage();
}

function initializeIngredientRow(row) {
    const removeBtn = row.querySelector('.remove-ingredient');
    const percentageInput = row.querySelector('.percentage-input');
    
    if (removeBtn) {
        removeBtn.addEventListener('click', function() {
            row.remove();
            updateTotalPercentage();
        });
    }
    
    if (percentageInput) {
        percentageInput.addEventListener('input', updateTotalPercentage);
    }
}

function updateTotalPercentage() {
    const percentageInputs = document.querySelectorAll('.percentage-input');
    let total = 0;
    
    percentageInputs.forEach(function(input) {
        const value = parseFloat(input.value) || 0;
        total += value;
    });
    
    const totalDisplay = document.getElementById('total-percentage');
    if (totalDisplay) {
        totalDisplay.textContent = total.toFixed(2);
        
        // Color coding for total percentage
        if (Math.abs(total - 100) <= 1) {
            totalDisplay.className = 'text-success fw-bold';
        } else if (Math.abs(total - 100) <= 5) {
            totalDisplay.className = 'text-warning fw-bold';
        } else {
            totalDisplay.className = 'text-danger fw-bold';
        }
    }
}

function getIngredientOptions() {
    // This would typically be populated from the server
    // For now, return empty string - will be populated by server-side rendering
    return '';
}

// Prediction Functions
function initializePredictionForms() {
    const predictBtn = document.getElementById('predict-btn');
    const predictForm = document.getElementById('prediction-form');
    
    if (predictBtn) {
        predictBtn.addEventListener('click', handlePrediction);
    }
    
    if (predictForm) {
        predictForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handlePrediction();
        });
    }
}

async function handlePrediction() {
    const loadingSpinner = document.getElementById('loading-spinner');
    const resultsContainer = document.getElementById('prediction-results');
    const predictBtn = document.getElementById('predict-btn');
    
    // Show loading state
    if (loadingSpinner) loadingSpinner.style.display = 'block';
    if (resultsContainer) resultsContainer.innerHTML = '';
    if (predictBtn) {
        predictBtn.disabled = true;
        predictBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Predicting...';
    }
    
    try {
        // Collect form data
        const formData = collectFormulationData();
        
        // Make API request
        const response = await fetch('/api/predict', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            displayPredictionResults(result.predictions || result.prediction);
        } else {
            showError('Prediction failed: ' + result.error);
        }
        
    } catch (error) {
        console.error('Prediction error:', error);
        showError('An error occurred during prediction. Please try again.');
    } finally {
        // Hide loading state
        if (loadingSpinner) loadingSpinner.style.display = 'none';
        if (predictBtn) {
            predictBtn.disabled = false;
            predictBtn.innerHTML = '<i class="fas fa-brain me-2"></i>Predict Properties';
        }
    }
}

function collectFormulationData() {
    const formData = {
        formulation_type: document.getElementById('formulation-type')?.value || 'pharmaceutical',
        temperature: parseFloat(document.getElementById('temperature')?.value) || 25.0,
        ph_target: parseFloat(document.getElementById('ph-target')?.value) || 7.0,
        mixing_time: parseFloat(document.getElementById('mixing-time')?.value) || 30.0,
        mixing_speed: parseFloat(document.getElementById('mixing-speed')?.value) || 500.0,
        ingredients: []
    };
    
    // Collect ingredients
    document.querySelectorAll('.ingredient-row').forEach(function(row) {
        const ingredientSelect = row.querySelector('.ingredient-select');
        const percentageInput = row.querySelector('.percentage-input');
        const functionSelect = row.querySelector('select[name="function"]');
        
        if (ingredientSelect && percentageInput && ingredientSelect.value && percentageInput.value) {
            // Get ingredient data from the select option
            const selectedOption = ingredientSelect.selectedOptions[0];
            const ingredientData = {
                id: parseInt(ingredientSelect.value),
                name: selectedOption.textContent,
                percentage: parseFloat(percentageInput.value),
                function: functionSelect ? functionSelect.value : '',
                // Add molecular descriptors if available from data attributes
                molecular_weight: parseFloat(selectedOption.dataset.molecularWeight) || 200.0,
                log_p: parseFloat(selectedOption.dataset.logP) || 2.0,
                solubility_water: parseFloat(selectedOption.dataset.solubilityWater) || 1000.0,
                melting_point: parseFloat(selectedOption.dataset.meltingPoint) || 100.0,
                density: parseFloat(selectedOption.dataset.density) || 1.0
            };
            
            formData.ingredients.push(ingredientData);
        }
    });
    
    return formData;
}

function displayPredictionResults(predictions) {
    const resultsContainer = document.getElementById('prediction-results');
    if (!resultsContainer) return;
    
    let html = '<div class="row">';
    
    // Handle both single prediction and multiple predictions
    const predictionData = predictions.predicted_value ? { 'single_property': predictions } : predictions;
    
    Object.entries(predictionData).forEach(([propertyName, prediction]) => {
        const cardClass = getPropertyCardClass(propertyName);
        
        html += `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card ${cardClass}">
                    <div class="card-body text-center">
                        <h6 class="card-title text-uppercase">${formatPropertyName(propertyName)}</h6>
                        <div class="prediction-value">${formatPredictionValue(prediction.predicted_value, propertyName)}</div>
                        <div class="confidence-interval">
                            CI: ${formatPredictionValue(prediction.confidence_interval_lower, propertyName)} - 
                            ${formatPredictionValue(prediction.confidence_interval_upper, propertyName)}
                        </div>
                        <small class="text-muted">Model: ${prediction.model_used}</small>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    
    // Add chart if multiple predictions
    if (Object.keys(predictionData).length > 1) {
        html += '<div class="mt-4"><canvas id="prediction-chart" width="400" height="200"></canvas></div>';
    }
    
    resultsContainer.innerHTML = html;
    
    // Create chart if multiple predictions
    if (Object.keys(predictionData).length > 1) {
        createPredictionChart(predictionData);
    }
    
    // Animate results
    resultsContainer.classList.add('fade-in');
}

function getPropertyCardClass(propertyName) {
    const classMap = {
        'viscosity': 'bg-primary text-white',
        'stability': 'bg-success text-white',
        'solubility': 'bg-info text-white',
        'bioavailability': 'bg-warning text-dark',
        'dissolution_rate': 'bg-secondary text-white',
        'texture_score': 'bg-dark text-white'
    };
    
    return classMap[propertyName] || 'bg-light';
}

function formatPropertyName(propertyName) {
    return propertyName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function formatPredictionValue(value, propertyName) {
    const units = {
        'viscosity': ' cP',
        'stability': '%',
        'solubility': ' mg/mL',
        'bioavailability': '%',
        'dissolution_rate': '%',
        'texture_score': '/10'
    };
    
    const unit = units[propertyName] || '';
    return parseFloat(value).toFixed(2) + unit;
}

function createPredictionChart(predictions) {
    const ctx = document.getElementById('prediction-chart');
    if (!ctx) return;
    
    const labels = Object.keys(predictions).map(formatPropertyName);
    const values = Object.values(predictions).map(p => p.predicted_value);
    const lowerBounds = Object.values(predictions).map(p => p.confidence_interval_lower);
    const upperBounds = Object.values(predictions).map(p => p.confidence_interval_upper);
    
    if (predictionChart) {
        predictionChart.destroy();
    }
    
    predictionChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Predicted Value',
                data: values,
                backgroundColor: 'rgba(13, 110, 253, 0.8)',
                borderColor: 'rgba(13, 110, 253, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Property Predictions'
                },
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Chart Initialization
function initializeCharts() {
    // Initialize any charts on the current page
    const chartElements = document.querySelectorAll('[data-chart]');
    chartElements.forEach(initializeChart);
}

function initializeChart(element) {
    const chartType = element.dataset.chart;
    const chartData = JSON.parse(element.dataset.chartData || '{}');
    
    switch (chartType) {
        case 'formulation-stats':
            createFormulationStatsChart(element, chartData);
            break;
        case 'ingredient-categories':
            createIngredientCategoriesChart(element, chartData);
            break;
    }
}

function createFormulationStatsChart(element, data) {
    new Chart(element, {
        type: 'doughnut',
        data: {
            labels: data.labels || [],
            datasets: [{
                data: data.values || [],
                backgroundColor: [
                    '#0d6efd',
                    '#198754',
                    '#ffc107',
                    '#dc3545'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Formulations by Type'
                }
            }
        }
    });
}

function createIngredientCategoriesChart(element, data) {
    new Chart(element, {
        type: 'bar',
        data: {
            labels: data.labels || [],
            datasets: [{
                label: 'Count',
                data: data.values || [],
                backgroundColor: 'rgba(13, 110, 253, 0.8)'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Ingredients by Category'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Utility Functions
function showError(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHtml);
    }
}

function showSuccess(message) {
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHtml);
    }
}

// Export functions for use in other scripts
window.FormulationApp = {
    addIngredientRow,
    handlePrediction,
    collectFormulationData,
    displayPredictionResults,
    showError,
    showSuccess
};
